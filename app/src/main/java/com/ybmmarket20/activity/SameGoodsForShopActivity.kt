package com.ybmmarket20.activity

import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackResourceProductClick
import com.ybmmarket20.common.splicingPageTitle2Entrance
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.AdapterUtils.addLocalTimeForRows
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.SearchSameGoodsForShopViewModel
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import kotlinx.android.synthetic.main.activity_same_goods_for_shop.*

/**
 * <AUTHOR>
 * @date 2022/4/15
 * @description 店铺同款商品
 */
@Router("SameGoodsForShopActivity")
class SameGoodsForShopActivity: BaseActivity() {

    val mViewModel by viewModels<SearchSameGoodsForShopViewModel>()
    var mData: MutableList<RowsBean> = mutableListOf()
    var mAdapter = GoodListAdapterNew(R.layout.item_goods_new, mData)
    var requestParams: RequestParams? = null
    var mEntrance = ""
    var mJgTrackBean: JgTrackBean? = null

    override fun getContentViewId(): Int = R.layout.activity_same_goods_for_shop

    override fun initData() {
        val goodsName = intent.getStringExtra("goodsName")
        val shopCodes = intent.getStringExtra("shopCodes")
        val shopName = intent.getStringArrayExtra("shopName")
        mEntrance = intent.getStringExtra(IntentCanst.JG_ENTRANCE) ?: ""
        mJgTrackBean = JgTrackBean().apply {
            pageId = JGTrackManager.TrackShopSameGoods.PAGE_ID
            title = JGTrackManager.TrackShopSameGoods.TITLE
            jgReferrer = <EMAIL>()
            jgReferrerTitle = JGTrackManager.TrackShopSameGoods.TITLE
            jgReferrerModule = JGTrackManager.TrackShopSameGoods.TITLE
            entrance = if (mEntrance.isEmpty()) JGTrackManager.TrackShopSameGoods.TITLE else splicingPageTitle2Entrance(
                    mEntrance,
                    JGTrackManager.TrackShopSameGoods.TITLE)
            url = <EMAIL>()

        }
        val masterStandardProductId = intent.getStringExtra("masterStandardProductId")
        setTitle(goodsName)
        rv.layoutManager = LinearLayoutManager(
                this,
                LinearLayoutManager.VERTICAL,
                false)
        rv.adapter = mAdapter.apply {
            jgTrackBean = mJgTrackBean

            resourceViewTrackListener = { rowsBean, position,_ ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
            }

            productClickTrackListener = { rowsBean, position,isBtnClick,mContent,number ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (productTag.isNotEmpty()) {
                            productTag += ","
                        }
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                // jgTrackResourceProductClick(
                //         url = this.getFullClassName(),
                //         module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                //         referrer = this.getFullClassName(),
                //         pageId = JGTrackManager.TrackShopSameGoods.PAGE_ID,
                //         title = JGTrackManager.TrackShopSameGoods.TITLE,
                //         resourceId = "",
                //         resourceName = "",
                //         resourceType = "",
                //         position = position,
                //         productId = rowsBean.productId ?: "",
                //         productName = rowsBean.productName ?: "",
                //         productType = "普通商品",
                //         productPrice = rowsBean.jgProductPrice ?: 0.0,
                //         productLabel = productTag,
                //         entrance = this.jgTrackBean?.entrance ?: "",
                //         navigation = "") // 极光埋点移除：移除店铺同款商品页面resource_click事件
            }
        }

        mAdapter.setEnableLoadMore(true)
        //监听加载更多
        mAdapter.setOnLoadMoreListener({ requestParams?.let { mViewModel.getSameGoodsForShopData(it.paramsMap) } }, rv)
        //获取数据
        mViewModel.liveDataViewModel.observe(this, Observer {
            if (it != null && it.dataList != null) {
                try {
                    mData.addAll(it.dataList)
                    // 请求并更新折后价
                    AdapterUtils.getAfterDiscountPrice(it.dataList, mAdapter)
                    addLocalTimeForRows<RowsBean>(mData)
                    requestParams = it.requestParams
                    mAdapter.notifyDataChangedAfterLoadMore(!it.isEnd)
                    mAdapter.flowData = FlowData.withInit().apply {
                        spType = it.sptype
                        spId = it.spid
                        sId = it.sid
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            dismissProgress()
        })
        //首次请求
        mViewModel.getSameGoodsForShopData(mapOf(
                "shopCodes" to (shopCodes?: ""),
                "masterStandardProductId" to (masterStandardProductId?: ""),
                "spFrom" to "1"
        ))
        showProgress()
        XyyIoUtil.track("action_mainLabelCommodityList", hashMapOf(
            "shopCode" to shopCodes,
            "commodityName" to goodsName
        ))
    }
}