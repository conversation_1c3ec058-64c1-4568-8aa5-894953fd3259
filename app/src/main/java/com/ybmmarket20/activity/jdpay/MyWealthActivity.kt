package com.ybmmarket20.activity.jdpay

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.text.TextUtils
import androidx.activity.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.analysys.ANSAutoPageTracker
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.pingan.bank.kyb_sdk.KybSdk
import com.pingan.bank.kyb_sdk.bean.KybCallStatusInfo
import com.ybmmarket20.R
import com.ybmmarket20.activity.CommonH5Activity
import com.ybmmarket20.activity.NongWebviewActivity
import com.ybmmarket20.activity.jdpay.adapter.MyWealthAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.MyWealthItem
import com.ybmmarket20.bean.PayNongData
import com.ybmmarket20.bean.ReqUrlJsonBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.showPayPop
import com.ybmmarket20.viewmodel.Mine2ViewModel
import com.ybmmarket20.viewmodel.MyWealthViewModel
import kotlinx.android.synthetic.main.activity_my_wealth.rvMyWealth
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject


/**
 * 我的财富
 */
@Router("mywealth")
class MyWealthActivity : BaseActivity(), ANSAutoPageTracker {

    private val mViewModel: MyWealthViewModel by viewModels()
    private val pingAnViewModel: Mine2ViewModel by viewModels()
    private var mPToken: String? = ""
    var isFirst = true

    companion object {
        fun jgTrackBtnClick(mContext: Context, btnName: String) {
            val params = java.util.HashMap<String, Any>()
            params[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMineWealth.PAGE_ID
            params[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMineWealth.TITLE
            params[JGTrackManager.FIELD.FIELD_REFERRER] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_URL] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = this.getFullClassName()
            params[JGTrackManager.FIELD.FIELD_MODULE] = "功能"
            params[JGTrackManager.FIELD.FIELD_BTN_NAME] = btnName
            // 移除极光埋点 - btn_click
            /*
            JGTrackManager.eventTrack(
                mContext,
                JGTrackManager.TrackMineWealth.EVENT_BTN_CLICK,
                params
            )
            */
        }
    }

    override fun getContentViewId(): Int = R.layout.activity_my_wealth

    override fun initData() {
        setTitle("我的财富")
        EventBus.getDefault().register(this)
        pingAnViewModel.pingAnPrePayLiveData.observe(this) { prePay ->
            dismissProgress()
            if (prePay.isSuccess) {
                mPToken = prePay.data?.platformToken
                prePay.data?.pinganLoginCheckUrl?.let { KybSdk.startWithYunReceiveMoney(this, it) }
            }
        }
        pingAnViewModel.jdPrePayLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess && !TextUtils.isEmpty(it.data)) {
                Intent(this, CommonH5Activity::class.java).apply {
                    putExtra("commonHtmlStr", it.data)
                    putExtra("url", "ybmpage://space?isShowCart=0")
                    putExtra("ybm_title", "")
                    startActivity(this)
                }
            }
        }

        pingAnViewModel.nongEPrePayLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess && !TextUtils.isEmpty(it.data.h5Url)) {
                Intent(this, NongWebviewActivity::class.java).apply {
                    putExtra("url", it.data.h5Url)
                    startActivity(this)
                }
            }
        }
        mViewModel.myWealthListLiveData.observe(this) {
            dismissProgress()
            var adapter = MyWealthAdapter(mutableListOf())
            if (it.isSuccess) {
                rvMyWealth.layoutManager =
                    LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
                adapter = MyWealthAdapter(it.data, {
                    showProgress()
                    pingAnViewModel.getPingAnPrePayUrl()
                }, { item ->
                    showProgress()
                    if (item.reqUrl.isNullOrEmpty()) {
                        pingAnViewModel.getJDPayLogin()
                    } else {
                        pingAnViewModel.getJDPayLogin(item.reqUrl)
                    }
                }, { bean ->
                    nongCallBack(bean)
                }, { bean ->
                    // 小雨点点击
                    showProgress()
                    mViewModel.xydPreApply()
                }, { bean ->
                    // 金蝶点击
                    jinDieCallBack(bean)
                })
            } else {
                adapter.emptyView = layoutInflater.inflate(R.layout.layout_empty_view, null)
            }
            rvMyWealth.adapter = adapter
        }
        mViewModel.wealthPreLiveData.observe(this) {
            dismissProgress()
            // 成功
            if(it.isSuccess){
                RoutersUtils.open("ybmpage://commonh5activity?url=${it.data.h5Url}&isShowCart=0")
            }
        }
        receiveBindCardSuccessRefresh()
        showProgress()
        mViewModel.queryMyWealthList()
    }
    private fun jinDieCallBack(bean: MyWealthItem) {
        if (bean.accountState == 1) {
            if (!bean.reqUrl.isNullOrEmpty()) {
                //如果使用router，url过长会被截取
                Intent(this, NongWebviewActivity::class.java).apply {
                    putExtra("url", bean.reqUrl)
                    startActivity(this)
                }
            } else {
                when (bean.state) {
                    0, 4, 9 -> {
                        Intent(this, CommonH5Activity::class.java).apply {
                            putExtra(
                                "url",
                                AppNetConfig.getApiHost() + "newstatic/#/jdApply?merchantId=${SpUtil.getMerchantid()}&merchantName=${SpUtil.getMerchantInfo().shopName}"
                            )
                            putExtra("ybm_title", "")
                            putExtra("isShowCart", "0")
                            startActivity(this)
                        }
                    }
                }
            }
        } else {
            ToastUtils.showShort("请先到平安商户页面进行开户、绑卡，开户成功后可重试")
        }
    }
    private fun nongCallBack(bean:MyWealthItem){
    //3-审批中；5-审批通过；7-已签署合同
    when (bean.pingAnCreditState) {
        3 -> {
            val title = "您已提交额度申请，待农行审批"
            val contentStr = "预计需要1-2个工作日，请耐心等待"
            val cancelStr = ""
            val confirmStr = "确定"
            val orderNum = ""
            val amount = ""
            showPayPop(
                this, title, contentStr,
                orderNum, amount, cancelStr, confirmStr, null,
                null
            )
        }

        5 -> {
            val title = "已通过申请"
            val contentStr =
                "请额度申请人前往【中国农业银行】app在线签约\n操作路径：中国农业银行-贷款-选择“助业快e贷”-完成申请签约"
            val cancelStr = "稍后签约"
            val confirmStr = "打开农行APP"
            val orderNum = ""
            val amount = ""
            showPayPop(
                this,
                title,
                contentStr,
                orderNum,
                amount,
                cancelStr,
                confirmStr,
                null
            ) {
                val reqUrl =
                    Gson().fromJson(bean.reqUrl, ReqUrlJsonBean::class.java)
                val intent = packageManager.getLaunchIntentForPackage(
                    reqUrl.androidScheme ?: ""
                )
                if (null != intent) {
                    // 如果找到了对应的Intent，则启动该应用
                    startActivity(intent)
                } else {
                    RoutersUtils.open(reqUrl.abchinaDirectUrl)
                }
            }
        }

        7 -> {
            val title = "已完成在线签约"
            val contentStr = "可在药帮忙app采购支付时使用。\n" +
                    "如需查询可用额度或还款，额度申请人可前往【中国农业银行】app操作执行"
            val cancelStr = "确定"
            val confirmStr = "打开农行APP"
            val orderNum = ""
            val amount = ""
            showPayPop(
                this, title, contentStr,
                orderNum, amount, cancelStr, confirmStr, null
            ) {
                val reqUrl =
                    Gson().fromJson(bean.reqUrl, ReqUrlJsonBean::class.java)
                val intent = packageManager.getLaunchIntentForPackage(
                    reqUrl.androidScheme ?: ""
                )
                if (null != intent) {
                    // 如果找到了对应的Intent，则启动该应用
                    startActivity(intent)
                } else {
                    RoutersUtils.open(reqUrl.abchinaDirectUrl)
                }
            }
        }

        else -> {
            showProgress()
            pingAnViewModel.getNongH5Url()
        }
    }
}
    /**
     * 绑卡成功需要刷新接口
     */
    private fun receiveBindCardSuccessRefresh() {
        LocalBroadcastManager.getInstance(applicationContext)
            .registerReceiver(object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    mViewModel.queryMyWealthList()
                }
            }, IntentFilter(IntentCanst.ACTION_MY_WEALTH_REFRESH))
    }

    /**
     * 通过EventBus收到KybCallStatusInfo的消息通知。
     * KybCallStatusInfo的消息通知是H5调用callHostNative方法
     * 原生这边会将调用callHostNative方法所传递的params 和method字段传递到EKybCallStatusInfo的data中消息中
     * 原生监听KybCallStatusInfo消息，然后自身做业务和逻辑处理。如果有返回结果
     * 通过KybSdk.hostCallbackH5(callbackInfo); 传递给H5
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun receviedKybStatusInfo(kybCallStatusInfo: KybCallStatusInfo) {
        val data = kybCallStatusInfo.jsonData
        val checkResultJson = kybCallStatusInfo.jsonData
        val callbackInfo = JSONObject()
        var checkoutResult = false
        try {
            val resultObj = JSONObject(checkResultJson).getJSONObject("params")
            if (TextUtils.equals(resultObj.getString("channelId"), "CCSS-CLOUDPAY")) {
                //支付验证
                checkoutResult =
                    TextUtils.equals(resultObj.getString("checkCode"), SpUtil.getMerchantid())
            } else if (TextUtils.equals(resultObj.getString("channelId"), "KYB")) {
                //免登录校验
                checkoutResult = TextUtils.equals(resultObj.getString("checkCode"), mPToken)
            }
        } catch (e: JSONException) {
            e.printStackTrace()
            checkoutResult = false
        }
        try {
            if (checkoutResult) {
                callbackInfo.put("status", "ok")
                callbackInfo.put("msg", "")
            } else {
                callbackInfo.put("status", "error")
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        KybSdk.hostCallbackH5(callbackInfo)
    }


    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault() != null) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackMineWealth.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackMineWealth.TITLE
        return properties
    }

    override fun registerPageUrl(): String = this.getFullClassName()
}