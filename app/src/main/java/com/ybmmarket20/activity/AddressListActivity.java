package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.analysys.ANSAutoPageTracker;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.AddressListAdapter;
import com.ybmmarket20.bean.AddressListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.MyImageSpan;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.Bind;

/**
 * 地址管理
 *
 * <AUTHOR>
 * @date 2017.7.19
 */
@Router({"addresslist", "addresslist/:list"})
public class AddressListActivity extends BaseActivity implements ANSAutoPageTracker {

    @Bind(R.id.rv_addreRecyclerView)
    CommonRecyclerView rv_addreRV;
    @Bind(R.id.tv_tips)
    TextView tvTips;

    List<AddressListBean> addressListData;
    AddressListAdapter adreListAdapter;
    protected AddressListBean addressBean;
    private String source = "";
    int REQUESTCODE = 1;
    boolean isNowAddressBean = false;

    @Override
    protected void initData() {
        setTitle("地址管理");
        addressBean = (AddressListBean) getIntent().getSerializableExtra("data");
        source = getIntent().getStringExtra("source");
        if (StringUtil.isEmpty(source)) {
            source = "";
        }
        rv_addreRV.setRefreshEnable(false);
        rv_addreRV.setShowAutoRefresh(false);
        rv_addreRV.setLoadMoreEnable(false);
        //设置项目间隔线
//        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
//        divider.setSize(1);
//        divider.setColor(UiUtils.getColor(R.color.color_EFEFEF));//#EEEEEE
//        rv_addreRV.addItemDecoration(divider);
        addressListData = new ArrayList<>();
        RecyclerViewHandler.sendEmptyMessage(0);
        getAddress();
    }


    /**
     * 进入编辑地址
     *
     * @param addressListBean 项目实体对象
     **/
    private void toUpdateAddress(AddressListBean addressListBean) {
        Intent intent = new Intent(AddressListActivity.this, AddressEditActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString("update", "true");
        bundle.putSerializable("data", addressListBean);
        bundle.putString("addressType",addressListBean.addressType);
        intent.putExtras(bundle);
        startActivityForResult(intent, REQUESTCODE);
        HashMap<String,Object> map = new HashMap<>();
        map.put(JGTrackManager.FIELD.FIELD_URL, JGTrackManager.TrackAddressList.URL);
        map.put(JGTrackManager.FIELD.FIELD_URL_DOMAIN, JGTrackManager.TrackAddressList.URL);
        map.put(JGTrackManager.FIELD.FIELD_REFERRER, AppUtilKt.getFullClassName(this));
        map.put(JGTrackManager.FIELD.FIELD_PAGE_ID, JGTrackManager.TrackAddressList.PAGE_ID);
        map.put(JGTrackManager.FIELD.FIELD_TITLE, JGTrackManager.TrackAddressList.TITLE);
        map.put(JGTrackManager.FIELD.FIELD_MODULE, "功能");
        map.put(JGTrackManager.FIELD.FIELD_BTN_NAME, "编辑");
        // 移除极光埋点 - btn_click
        // JGTrackManager.Companion.eventTrack(this, JGTrackManager.TrackAddressList.EVENT_BTN_CLICK, map);
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_address_list;
    }

    /**
     * 获取地址
     **/
    private void getAddress() {

        if (rv_addreRV == null) {
            return;
        }
        showProgress();
        RequestParams params = new RequestParams();
        String merchantid = SpUtil.getMerchantid();
        params.put("merchantId", merchantid);//商户ID
        if (!StringUtil.isEmpty(source) && source.equals("pay")) {
            if (addressBean != null && addressBean.id > 0) {
                params.put("addressId", addressBean.id + "");//地址id
            }
        }
        HttpManager.getInstance().post(AppNetConfig.GET_ADDRESS_NEW, params, new BaseResponse<List<AddressListBean>>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

            @Override
            public void onSuccess(String content, BaseBean<List<AddressListBean>> obj, List<AddressListBean> data) {
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    if (data != null && data.size() > 0) {
                        if (addressListData == null) {
                            addressListData = new ArrayList<>();
                        }
                        addressListData.clear();
                        addressListData.addAll(data);
                        if (!TextUtils.isEmpty(data.get(0).text)) {
                            tvTips.setVisibility(View.VISIBLE);
                            tvTips.setText(data.get(0).text);
//                            bindImageData(data.get(0).text);
                        } else {
                            tvTips.setVisibility(View.GONE);
                        }
//                        if (!TextUtils.isEmpty(data.get(0).text)) {
//                            tvTips.setVisibility(View.VISIBLE);
//                            tvTips.setText("" + data.get(0).text);
//                        } else {
//                            tvTips.setVisibility(View.GONE);
//                        }
                        RecyclerViewHandler.sendEmptyMessage(0);
                    }
                }
            }
        });
    }

    private void bindImageData(String text) {

        List<Integer> list = new ArrayList<>();
        list.add(R.drawable.icon_address_message_tips);

        SpannableStringBuilder shopName = getShopNameIcon(text, list);
        if (!TextUtils.isEmpty(shopName)) tvTips.setText(shopName);
    }

    private SpannableStringBuilder getShopNameIcon(String shopName, List<Integer> icons) {
        if (icons != null && icons.size() > 0) {
            SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = getResources().getDrawable(icons.get(i));
                drawable.setBounds(0, 0, ConvertUtils.dp2px(15), ConvertUtils.dp2px(15));

                MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                //占个位置
                spannableString.insert(0, "-");
                spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
            return spannableString;
        }
        return null;
    }

    /****
     * 刷新数据
     * **/
    public Handler RecyclerViewHandler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (rv_addreRV == null) {
                return;
            }
            boolean isShow = false;
            if (!StringUtil.isEmpty(source) && source.equals("pay")) {
                isShow = true;
            }
            adreListAdapter = new AddressListAdapter(R.layout.list_item_address, addressListData, isShow);
            adreListAdapter.setItemEditListener(new AddressListAdapter.OnAddressListItemEditListener() {
                @Override
                public void onEditClick(AddressListBean addressListBean) {
                    toUpdateAddress(addressListBean);
                }
            });
            adreListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int i) {
                    if (!StringUtil.isEmpty(source) && source.equals("pay")) {
                        addressBean = addressListData.get(i);
                        setResult();
                    }
                }

            });
            rv_addreRV.setAdapter(adreListAdapter);

            //back返回
            setLeft(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setResult();
                }
            });
        }
    };

    private void setResult() {

        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putSerializable("addressIndex", addressBean);
        intent.putExtras(bundle);
        setResult(Activity.RESULT_OK, intent);
        finish();
    }

    //切换地址返回更新数据
    @SuppressLint("MissingSuperCall")
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUESTCODE && resultCode == RESULT_OK) {
            AddressListBean address_Bean = (AddressListBean) data.getSerializableExtra("addressIndex");
            if (addressBean != null && address_Bean != null && address_Bean.id == addressBean.id) {
                addressBean = address_Bean;
            }

            getAddress();
//            if (AddressListData != null && AddressListData.size() > 0) {
//                AddressListData.set(AddressListData.indexOf(address_Bean), address_Bean);
//            }
//            RecyclerViewHandler.sendEmptyMessage(0);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            setResult();
        }
        return false;
    }

    @Override
    public String getPageName() {
        String pageName = "";
        if (source.endsWith("pay")) {
            pageName = XyyIoUtil.PAGE_CONFIRMEDORDERSELECTADDRESS;
        } else {
            pageName = XyyIoUtil.PAGE_MEADDRESSMANAGEMENT;
        }
        return pageName;
    }

    @Override
    public Map<String, Object> registerPageProperties() {
        HashMap<String,Object> properties = new HashMap<>();
        properties.put(JGTrackManager.FIELD.FIELD_PAGE_ID,JGTrackManager.TrackAddressList.PAGE_ID);
        properties.put(JGTrackManager.FIELD.FIELD_TITLE,JGTrackManager.TrackAddressList.TITLE);
        return properties;
    }

    @Override
    public String registerPageUrl() {
        return AppUtilKt.getFullClassName(AddressListActivity.this);
    }
}
