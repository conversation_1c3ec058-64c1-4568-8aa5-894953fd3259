package com.ybmmarket20.home.newpage

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.app.hubert.guide.NewbieGuide
import com.app.hubert.guide.core.Controller
import com.app.hubert.guide.model.GuidePage
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.jeremyliao.liveeventbus.LiveEventBus
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.xyy.canary.utils.LogUtil
import com.xyy.canary.utils.SharePreUtil
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeConfigBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.homesteady.ActionListProductClick
import com.ybmmarket20.bean.homesteady.FastEntry
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.bean.homesteady.FeedComponentBean
import com.ybmmarket20.bean.homesteady.HomeTabBean
import com.ybmmarket20.bean.homesteady.PageListBuild
import com.ybmmarket20.bean.homesteady.TabItem
import com.ybmmarket20.bean.homesteady.Tabbar
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.LiveEventBusManager
import com.ybmmarket20.common.TrackManager
import com.ybmmarket20.common.TrackManager.TrackHome.TRACK_HOME_TAB_PAGE_ID
import com.ybmmarket20.common.TrackManager.TrackNewBieGuide.EVENT_BEGINNER_GUIDANCE_EXPOSURE
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgTrackHomeBtnClick
import com.ybmmarket20.common.jgTrackHomeResourceClick
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.home.newpage.adapter.ConcatAtmosphereHeadPictureAdapter
import com.ybmmarket20.home.newpage.adapter.ConcatCapsuleAdvertisementAdapter
import com.ybmmarket20.home.newpage.adapter.ConcatFastEntryAdapter
import com.ybmmarket20.home.newpage.adapter.ConcatFrequentPurchaseListAdapter
import com.ybmmarket20.home.newpage.adapter.ConcatPorcelainTilesAdapter
import com.ybmmarket20.home.newpage.adapter.ConcatSuperValueListAdapter
import com.ybmmarket20.home.newpage.adapter.HomeFeedStreamAdapter
import com.ybmmarket20.home.newpage.adapter.HomeFeedStreamAdapter.Companion.AD_BANNER_VIEW_TYPE
import com.ybmmarket20.home.newpage.adapter.HomeFeedStreamAdapter.Companion.AD_VIEW_TYPE
import com.ybmmarket20.home.newpage.adapter.HomeFeedStreamAdapter.Companion.COMMODITY_VIEW_TYPE
import com.ybmmarket20.home.newpage.bean.Affordable
import com.ybmmarket20.home.newpage.bean.AffordableFloorContent
import com.ybmmarket20.home.newpage.bean.AtmosphereImage
import com.ybmmarket20.home.newpage.bean.AtmosphereImageContentListBean
import com.ybmmarket20.home.newpage.bean.BottomAdvertisement
import com.ybmmarket20.home.newpage.bean.BottomAdvertisementContent
import com.ybmmarket20.home.newpage.bean.Capsule
import com.ybmmarket20.home.newpage.bean.CapsuleAdvertisementItemBean
import com.ybmmarket20.home.newpage.bean.FrequentPurchase
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamBean
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamBean.Companion.FEED_STREAM_PRODUCT_TYPE
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamResponse
import com.ybmmarket20.home.newpage.bean.NewSearchBox
import com.ybmmarket20.home.newpage.bean.RecommendCommodity
import com.ybmmarket20.home.newpage.bean.RecommendCommodityItemBean
import com.ybmmarket20.utils.AdapterUtils.getHomeFeedAfterDiscountPrice
import com.ybmmarket20.utils.HomePageInfoSpUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.HomeAptitudeViewModel
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ydmmarket.report.ReportManager
import kotlinx.android.synthetic.main.fragment_home_tab_common.cl_ad_bottom
import kotlinx.android.synthetic.main.fragment_home_tab_common.iv_ad
import kotlinx.android.synthetic.main.fragment_home_tab_common.iv_close
import kotlinx.android.synthetic.main.fragment_home_tab_common.rv_content
import kotlinx.android.synthetic.main.fragment_home_tab_common.smart_refresh_layout
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * HomeTabCommonFragment
 *
 * 整体页面用的RecycleView+ConcatAdapter+StaggeredGridLayoutManager
 * 如果有添加新的子Adapter 需要充满整个屏幕 可参考mConcatAtmosphereHeadPictureAdapter等重写onViewAttachedToWindow方法
 * @property mConcatAtmosphereHeadPictureAdapter ConcatAtmosphereHeadPictureAdapter
 * @property mCapsuleAdvertisementAdapter ConcatCapsuleAdvertisementAdapter
 * @property mConcatPorcelainTilesAdapter ConcatPorcelainTilesAdapter
 * @property mConcatFrequentPurchaseListAdapter ConcatSuperValueFloorAdapter
 * @property mConcatFeedStreamAdapter HomeFeedStreamAdapter
 *
 *    当前页面是配合ViewPager2 所以会预加载， 所以把加载请求放着OnResume去处理
 */

class HomeTabCommonFragment : HomeTabCommonAnalysisFragment() {

    //是否第一次加载
    private var isFirst = true
    private var isFirstLoadBottomAd = true

    private lateinit var mConcatAdapter: ConcatAdapter
    private val mViewModel: HomeAptitudeViewModel by viewModels() //共用父类viewModel
    private var tabId = "0"
    private var tabType = "0"
    private var tabName = ""
    private var isHomePage = false //是否是首页
    private var bottomAdPosition: Int = 0 //记录底部广告位 每次刷新要变
    private var isFeedStreamRefreshCount = 0 //Feed流刷新次数

    private var isCloseBottomAd = false // 是否用户关闭底部广告
    private var mCurrentBottomAdUrl = "" //记录当前广告跳转连接 埋点用
    //是否完成的新手引导 因为组件里面只要显示了就会记录次数， 如果用户没看完杀死app 这时就要重置重新记录
    private var isFinishedNewBieGuide = false
    private var scrollDistance = 0 //RecycleView滑动距离
    private var homeTabBean: HomeTabBean? = null
    var mIndexSearchListener: IIndexSearchListener? = null
    //搜索
    var indexSearch: NewSearchBox? = null
    var mDefaultBottomTabClick = false

    companion object {
        private const val HOME_SPAN_COUNT = 2

        const val ARGUMENTS_HOME_TAB_ID = "arguments_home_tab_id"
        const val ARGUMENTS_HOME_TAB_TYPE = "arguments_home_tab_type"
        const val ARGUMENTS_HOME_TAB_NAME = "arguments_home_tab_name"
        const val ARGUMENTS_HOME_IS_HOME = "arguments_home_is_home"
        const val ARGUMENTS_HOME_TAB_BEAN = "arguments_home_tab_bean"
        const val NEWBIE_GUIDE_LABEL = "newbieGuide_home_page"

        const val WHERE_CLICK_MAIN_TITLE = 1 //主标题
        const val WHERE_CLICK_BLANK = 2
        const val WHERE_CLICK_SUBTITLE_TITLE = 3 //副标题

        private const val KEY_IS_FINISHED_NEWBIE_GUIDE = "isFinishedNewBieGuide"
    }

    //氛围头图
    private val mConcatAtmosphereHeadPictureAdapter: ConcatAtmosphereHeadPictureAdapter by lazy {
        ConcatAtmosphereHeadPictureAdapter().apply {
            mHomeJgspid = homeTabBean?.jgspid
            mOnItemClickListener = { mSuperData, atmosphereImageContent: AtmosphereImageContentListBean ->
                atmosphereImageContent.hrefUrl?.let {
                    var url = it
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["referrerModule"] = if (atmosphereImageContent.activityName.isNullOrEmpty()) "首页-氛围图" else "首页-氛围图-${atmosphereImageContent.activityName?:""}"
                    mParams["entrance"] = "首页-${tabName}(氛围图-${atmosphereImageContent.activityName?:""})"
                    url = splicingUrlWithParams(url,mParams)
                    RoutersUtils.open(url)
                }

                val map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_URL, atmosphereImageContent.hrefUrl ?: "")
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_HEAD_PICTURE_CLICK, map)
                val module = "氛围图"
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),module,atmosphereImageContent.activityName?:"")
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),module,tabName)
            }
        }
    }

    //广告胶囊位
    private val mCapsuleAdvertisementAdapter: ConcatCapsuleAdvertisementAdapter by lazy {
        ConcatCapsuleAdvertisementAdapter().apply {
            homeJgspid = homeTabBean?.jgspid
            mOnItemClickListener = { mCapsuleData, capsuleAdvertisementItemBean,model,offset ->
                capsuleAdvertisementItemBean.hrefUrl?.let {
                    var url = it
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["jgspid"] = capsuleAdvertisementItemBean.jgspid ?: ""
                    mParams["referrerModule"] = if (capsuleAdvertisementItemBean.activityName.isNullOrEmpty()) "首页-胶囊位" else "首页-胶囊位-${capsuleAdvertisementItemBean.activityName?:""}"
                    mParams["entrance"] = "首页-${tabName}（胶囊位-${capsuleAdvertisementItemBean.activityName?:""}）"
                    url = splicingUrlWithParams(url,mParams)
                    RoutersUtils.open(url)
                }
                capsuleClickTrack(model, offset, capsuleAdvertisementItemBean)
                val module = "胶囊位"

                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),module,capsuleAdvertisementItemBean.activityName?:"")
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),module,tabName)
            }
        }
    }

    //金刚位
    private val mConcatFastEntryAdapter: ConcatFastEntryAdapter by lazy {
        ConcatFastEntryAdapter().apply {
            mHomeJgspid = homeTabBean?.jgspid
            pageId = getPageId()
            module = 1
            mComponentExposureListener = { superData->
            }
            mOnItemClickTrackListener = { superData, model, offset, url,item ->
                fastEntryClickTrack(model, offset, url)
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),"金刚位",item.entry?:"")
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),"金刚位",tabName)
                // 移除极光埋点 - action_list_product_click (保留搜索页面)
                // reportActionListProductClick(superData, item)
            }
            jgTrackBean = JgTrackBean(
                    jgReferrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL,
                    jgReferrerTitle = JGTrackManager.TrackHomePage.TITLE,
                    jgReferrerModule = "首页-金刚位",
                    module="首页-金刚位",
                    pageId = JGTrackManager.TrackHomePage.PAGE_ID,
                    title = JGTrackManager.TrackHomePage.TITLE,
                    entrance = "首页-${tabName}(金刚位)")
        }
    }


    //瓷片楼层
    private val mConcatPorcelainTilesAdapter: ConcatPorcelainTilesAdapter by lazy {
        ConcatPorcelainTilesAdapter().apply {
            mHomeJgspid = homeTabBean?.jgspid
            mOnItemClickListener = { mSuperData, itemBean,type: Int, offset: Int, content: String,where:Int ->
                itemBean.hrefUrl?.let {
                    var url = addTrackField2Url(it,pageId = getPageId(), module = 2,"")
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["referrerModule"] = if (itemBean.mainTitleText.isNullOrEmpty()) "首页-推荐位" else "首页-推荐位-${itemBean.mainTitleText}"
                    mParams["entrance"] = "首页-${tabName}(推荐位-${itemBean.activityName?:""})"
                    url = splicingUrlWithParams(url,mParams)
                    RoutersUtils.open(url)
                }
                //商品点击的埋点在mOnProductItemClickListener 这里面
                porcelainTilesItemClickTrack(where, type, offset, content)
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),"推荐位",content.ifEmpty { itemBean.mainTitleText?:"" })
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),"推荐位",tabName)
            }

            mOnProductItemClickListener = { mSuperData, itemBean: RecommendCommodityItemBean, rowBean: RowsBean, type: Int, offset: Int ->
                itemBean.hrefUrl?.let {
                    val anchorProductStatus = rowBean.actPt?.assembleStatus?.toString()?:""
                    var url = addTrackField2Url(it,pageId = getPageId(), module = 2,offset.toString(), anchorProductId = rowBean.productId?:"", anchorProductStatus = anchorProductStatus)
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["referrerModule"] = if (itemBean.mainTitleText.isNullOrEmpty()) "首页-推荐位" else "首页-推荐位-${itemBean.mainTitleText?:""}"
                    mParams["entrance"] = "首页-${tabName}(推荐位-${itemBean.activityName?:""})"
                    url = splicingUrlWithParams(url,mParams)
                    RoutersUtils.open(url)
                }
                porcelainTilesProductClickTrack(type, offset, rowBean)

                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),"推荐位",itemBean.mainTitleText?:"")
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),"推荐位",tabName)
            }

        }
    }

    //常购清单
    private val mConcatFrequentPurchaseListAdapter: ConcatFrequentPurchaseListAdapter by lazy {
        ConcatFrequentPurchaseListAdapter().apply {
//            navigation = tabName
            mHomeJgspid = homeTabBean?.jgspid
            mOnItemClickListener = { frequentPurchase: FrequentPurchase, type: Int, content: String,where:Int ->
                val mTitle = frequentPurchase.frequentPurchaseContent?.frequentPurchaseFloorList?.let { if (it.size > 0) it[0].mainTitleText else "" }?:""
                val data = frequentPurchase.frequentPurchaseContent?.frequentPurchaseFloorList?.let { if (it.size > 0) it[0] else null }
                FrequentPurchaseListActivity.launchActivity(requireActivity(), mTitle, entrance = "首页-${tabName}(常购清单-${mTitle})")
                frequentPurchaseClickTrack(where, type, content)


                val module = "常购清单"
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),module,content.ifEmpty { mTitle })
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),module,tabName)
            }

            mOnProductItemClickListener ={ frequentPurchase: FrequentPurchase, rowsBean: RowsBean, offset: Int ->
                val mTitle = frequentPurchase.frequentPurchaseContent?.frequentPurchaseFloorList?.let { if (it.size > 0) it[0].activityName else "" } ?: ""
                val data = frequentPurchase.frequentPurchaseContent?.frequentPurchaseFloorList?.let { if (it.size > 0) it[0] else null }
                FrequentPurchaseListActivity.launchActivity(requireActivity(), mTitle,rowsBean.productId,entrance = "首页-${tabName}(常购清单-${mTitle})")

                frequentPurchaseProductClickTrack(offset,rowsBean)
                val module = "常购清单"
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),module,mTitle)
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),module,tabName)
            }
        }
    }

    //超值清单
    private val mConcatSuperValueListAdapter: ConcatSuperValueListAdapter by lazy {
        ConcatSuperValueListAdapter().apply {
//            navigation = tabName
            mHomeJgspid = homeTabBean?.jgspid
            mOnItemClickListener = { superData: Affordable?, affordableFloorContent: AffordableFloorContent, type: Int, content: String, where: Int ->
                affordableFloorContent.hrefUrl?.let {
                    var mUrl = addTrackField2Url(it,pageId = getPageId(), module = 4,"")
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["referrerModule"] = if (affordableFloorContent.activityName.isNullOrEmpty()) "首页-超值清单" else "首页-超值清单-${affordableFloorContent.activityName}"
                    mParams["entrance"] = "首页-${tabName}（超值清单-${affordableFloorContent.activityName?:""}）"
                    mUrl = splicingUrlWithParams(mUrl,mParams)
                    RoutersUtils.open(mUrl)
                }

                superValueClickTrack(where, type, content)

                val title = affordableFloorContent.activityName ?:""
                val module = "超值清单"
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),module, content.ifEmpty { title })
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),module,tabName)
            }

            mOnProductItemClickListener = { superData: Affordable?, affordableFloorContent: AffordableFloorContent, rowsBean: RowsBean, offset: Int ->
                affordableFloorContent.hrefUrl?.let {
                    val anchorProductStatus = rowsBean.actPt?.assembleStatus?.toString()?:""
                    var mUrl = addTrackField2Url(it,pageId = getPageId(), module = 4,offset.toString(),anchorProductId = rowsBean.productId?:"", anchorProductStatus = anchorProductStatus)
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["referrerModule"] = if (affordableFloorContent.activityName.isNullOrEmpty()) "首页-超值清单" else "首页-超值清单-${affordableFloorContent.activityName}"
                    mParams["entrance"] = "首页-${tabName}（超值清单-${affordableFloorContent.activityName?:""}）"
                    mUrl = splicingUrlWithParams(mUrl,mParams)

                    RoutersUtils.open(mUrl)
                }

                superValueProductClickTrack(offset,rowsBean)

                val title = affordableFloorContent.activityName ?:""
                val module = "超值清单"
                requireActivity().jgTrackHomeBtnClick(<EMAIL>(),module,title)
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),module,tabName)
            }
        }
    }

    //商品广告流
    private val mConcatFeedStreamAdapter: HomeFeedStreamAdapter by lazy {
        HomeFeedStreamAdapter().apply {
            pageId = getPageId()
            mOnItemClickListener = { position: Int, url: String, clickType: Int,rowBean:RowsBean?, componentBean: FeedComponentBean? ->

                val module = when (clickType) {

                    AD_BANNER_VIEW_TYPE -> 5

                    AD_VIEW_TYPE -> 7

                    COMMODITY_VIEW_TYPE -> 7

                    else -> 0
                }

                feedStreamClickTrack(clickType,position, url, rowBean)
                if (clickType == COMMODITY_VIEW_TYPE){
                    val mParams = Bundle().apply {
                        putString(IntentCanst.PRODUCTID,rowBean?.id.toString())
                        putString(IntentCanst.JG_REFERRER,<EMAIL>())
                        putString(IntentCanst.JG_REFERRER_TITLE , "首页")
                        putString(IntentCanst.JG_REFERRER_MODULE , "首页-商品列表")
                        putString(IntentCanst.JG_ENTRANCE, "首页-${tabName}(商品列表)")
                        putInt("pageId", getPageId())
                        putInt("module", module)
                        putString("offset", (position+1).toString())
                        putString("sourceType", rowBean?.sourceType?:"")
                        putSerializable(IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN, ReportPDExtendOuterBean().apply {
                                sptype = componentBean?.sptype ?: ""
                                jgspid = componentBean?.jgspid?:""
                                sid = componentBean?.sid?:""
                                resultCnt = 0
                                pageNo = componentBean?.pageNum?.toInt()
                                pageSize = componentBean?.pageSize?.toInt()
                                totalPage = componentBean?.pageSize?.toInt()
                                keyWord = ""
                                searchSortStrategyId = ""
                                operationId = ""
                                operationRank = null
                                listPositionType = rowBean?.positionType.toString()
                                listPositionTypename = rowBean?.positionTypeName.toString()
                                component_position = componentBean?.componentPosition //组件序号
                                component_name = componentBean?.pageName //组件名称
                                component_title = componentBean?.componentTitle //组件标题
                            }
                        )
                    }
                    val intent = Intent(context, ProductDetailActivity::class.java)
                    intent.putExtras(mParams)
                    context?.startActivity(intent)
                }else{
                    var url = addTrackField2Url(url,pageId = getPageId(), module = module, offset = (position+1).toString(), sourceType = rowBean?.sourceType?:"")
                    val mParams:HashMap<String,Any> = hashMapOf()
                    mParams["referrer"] = <EMAIL>()
                    mParams["referrerTitle"] = "首页"
                    mParams["referrerModule"] = if (clickType == AD_BANNER_VIEW_TYPE)"首页-商品列表banner" else "首页-商品列表广告"
                    mParams["entrance"] = if (clickType == AD_BANNER_VIEW_TYPE) "首页-${tabName}(商品列表banner)" else "首页-${tabName}(商品列表广告)"
                    url = splicingUrlWithParams(url,mParams)
                    RoutersUtils.open(url)
                }
                requireActivity().jgTrackHomeResourceClick(<EMAIL>(),"商品列表",tabName)
            }
        }
    }

    override fun getLayoutId(): Int = R.layout.fragment_home_tab_common

    override fun initData(content: String?) {
        arguments?.apply {
            tabId = getString(ARGUMENTS_HOME_TAB_ID, "0")
            tabType = getString(ARGUMENTS_HOME_TAB_TYPE, "0")
            tabName = getString(ARGUMENTS_HOME_TAB_NAME, "")
            isHomePage = getBoolean(ARGUMENTS_HOME_IS_HOME, false)
            homeTabBean = getSerializable(ARGUMENTS_HOME_TAB_BEAN) as HomeTabBean?
        }

        isFinishedNewBieGuide = SharePreUtil.getBoolean(requireActivity(),KEY_IS_FINISHED_NEWBIE_GUIDE,false)

        initObserver()
        initRecycleView()
    }

    //初始化缓存数据
    private fun initCacheData(){
        HomePageInfoSpUtil.getHomePageCommonModuleResponse()?.let {
            mViewModel.setCacheCommonModuleData(BaseBean.newSuccessBaseBean(it))
        }

        HomePageInfoSpUtil.getHomePageCommonFeedStreamResponse()?.let {
            mViewModel.setCacheFeedStreamData(BaseBean.newSuccessBaseBean(it))
        }

    }



    private fun toRequest(isRefresh: Boolean) {
        lifecycleScope.launch {
            val deferred1 = async {
                if (isRefresh) { //加载时只请求feed流
                    mViewModel.getTabModel(tabId, tabType)
                }
            }
            val deferred2 = async {
                mViewModel.getFeedStream(tabId, tabType, isRefresh, homeTabBean?.sptype, homeTabBean?.jgspid, homeTabBean?.sid)
            }
            val awaitAll = awaitAll(deferred1, deferred2)
            //数据加载完再显示新手引导
            showNewbieGuidePage()
        }

    }

    private fun initObserver() {


        rv_content.addOnScrollListener(
        object : RecyclerView.OnScrollListener() {
            override fun onScrolled(
                    recyclerView: RecyclerView,
                    dx: Int,
                    dy: Int
            ) {
                super.onScrolled(recyclerView, dx, dy)
                scrollDistance += dy
                handleScroll()
            }

            override fun onScrollStateChanged(
                    recyclerView: RecyclerView,
                    newState: Int
            ) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // 没有滑动
                    LiveEventBus.get(LiveEventBusManager.HomeBus.BUS_SHOW_BIG_WHEEL,Boolean::class.java).post(true)
                } else {
                    LiveEventBus.get(LiveEventBusManager.HomeBus.BUS_SHOW_BIG_WHEEL,Boolean::class.java).post(false)
                }
            }
        })

        smart_refresh_layout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                isFeedStreamRefreshCount++
                resetReportRecord()
                trackHomePagePv(requireActivity(), mTrackData)
                toRequest(true)
                LocalBroadcastManager.getInstance(notNullActivity).sendBroadcast(Intent(IntentCanst.REFRESH_HOT_SEARCH))
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                toRequest(false)
            }
        })

        iv_close.setOnClickListener {
            cl_ad_bottom.isVisible = false
            isCloseBottomAd = true

            val map = hashMapOf<String,Any>(
                    Pair(TrackManager.FIELD_URL,mCurrentBottomAdUrl),
                    Pair(TrackManager.FIELD_PAGE_ID,getPageId()),
            )

            TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_BOTTOM_ADVERTISING_OFF_CLICK, map)
        }

        cl_ad_bottom.setOnClickListener {
            RoutersUtils.open(mCurrentBottomAdUrl)
            val map = hashMapOf<String,Any>(Pair(TrackManager.FIELD_URL,mCurrentBottomAdUrl),
                    Pair(TrackManager.FIELD_PAGE_ID,getPageId())
            )
            TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_BOTTOM_ADVERTISING_CLICK, map)
        }


        mViewModel.refreshLoadLiveData.observe(this) { isRefresh ->
            if (isRefresh) {
                smart_refresh_layout.finishRefresh()
            } else {
                smart_refresh_layout.finishLoadMore()
            }
        }

//        mViewModel.loadingLiveData.observe(this){
//            if (it) showProgress() else dismissProgress()
//        }

        mViewModel.canLoadLiveData.observe(this){
            smart_refresh_layout.setEnableLoadMore(it)
        }

        mViewModel.tabModelLiveData.observe(this) { result ->
            //delay一会是因为防止RecycleView还在计算或者滚动的时候刷新数据
            lifecycleScope.launch {
                if (result.isSuccess) {
                    if (isHomePage){
                        HomePageInfoSpUtil.setHomePageCommonModuleResponse(result.data)
                    }
                    val mData = result.data.modules ?: return@launch
                    //氛围头图 仅tab页有
                    var topImage: AtmosphereImage? = null
                    //胶囊位（仅首页有）
                    var bannerCaosule: Capsule? = null
                    //快捷入口
                    var fastEntry: FastEntry? = null
                    //推荐楼层
                    var recommendationProduct: RecommendCommodity? = null
                    //常购清单 tab页没有常购清单
                    var frequentPurchaseList: FrequentPurchase? = null
                    //超值清单
                    var valueRecommendation: Affordable? = null
                    var newIndexBottomView: BottomAdvertisement? = null
                    val newIndexTabbar = mData.newIndexTabbar

                    if (isHomePage) {
                        topImage = null
                        bannerCaosule = mData.bannerCapsule
                        fastEntry = mData.iconEntry
                        recommendationProduct = mData.recommendationProduct
                        frequentPurchaseList = mData.frequentPurchaseList
                        valueRecommendation = mData.valueRecommendation
                        newIndexBottomView = mData.newIndexBottomView
                        indexSearch = mData.newIndexSearch
                    } else {
                        topImage = mData.newTabTopImage
                        bannerCaosule = null
                        fastEntry = mData.tabFastEntry
                        recommendationProduct = mData.tabRecommendationProduct
                        frequentPurchaseList = null
                        valueRecommendation = mData.tabValueRecommendation
                        newIndexBottomView = mData.newTabBottomView
                        indexSearch = mData.tabSearch
                    }

                    //头图
                    topImage?.let {
                        mConcatAtmosphereHeadPictureAdapter.mSuperData = it
                        val map = HashMap<String,Any>().apply {
                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                        }
                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_HEAD_PICTURE_EXPOSURE,map)
                    }?:run {
                        mConcatAtmosphereHeadPictureAdapter.mSuperData = null
                    }

                    //胶囊位
                    bannerCaosule?.let {
                        delay(100)
                        mCapsuleAdvertisementAdapter.mCapsuleData = it

                        val map = HashMap<String,Any>().apply {
                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                        }
                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_CAROUSEL_BANNER_EXPOSURE,map)
                    }?: kotlin.run {
                        mCapsuleAdvertisementAdapter.mCapsuleData = null
                    }

                    //快捷入口
                    fastEntry?.let {
                        delay(100)
                        mConcatFastEntryAdapter.mData = it

                        val map = HashMap<String,Any>().apply {
                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                        }
                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_ICON_EXPOSURE,map)
                    }?:run {
                        mConcatFastEntryAdapter.mData = null
                    }

                    //推荐 瓷片
                    recommendationProduct?.let {
                        delay(100)
                        mConcatPorcelainTilesAdapter.mData = it
                        val map = HashMap<String,Any>().apply {
                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                        }
                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_RECOMMENDED_FLOORS_EXPOSURE,map)
                    }?:run {
                        mConcatPorcelainTilesAdapter.mData = null
                    }

                    //常购清单
                    frequentPurchaseList?.let {
                        delay(100)
                        mConcatFrequentPurchaseListAdapter.mData = it
                        val map = HashMap<String,Any>().apply {
                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                        }
                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_REGULAR_LIST_EXPOSURE,map)
                    }?:run {
                        mConcatFrequentPurchaseListAdapter.mData = null
                    }

                    //超值清单
                    valueRecommendation?.let {
                        delay(100)
                        mConcatSuperValueListAdapter.mData = it

                        val map = HashMap<String,Any>().apply {
                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                        }
                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_SPECIAL_FLOORS_EXPOSURE,map)
                    }?:run {
                        mConcatSuperValueListAdapter.mData = null
                    }

                    newIndexBottomView?.bottomAdvertisementContent?.let {
                        delay(100)
                        initBottomAd(it)
                    }?: kotlin.run {
                        initBottomAd(null)
                    }

                    //搜索
                    indexSearch?.let {
                        mIndexSearchListener?.onIndexSearch(it)
                    }

                    newIndexTabbar?.let {
                        if (mViewModel.isLoadHomeBottomTab) return@let
                        mViewModel.isLoadHomeBottomTab = true
                        setTabs(it)
                        try {
                            if (!mDefaultBottomTabClick) {
                                mDefaultBottomTabClick = true
                                (notNullActivity as MainActivity).onBottomTabDefaultClick(it.tabContent?.tabList?.get(0)?.trackData)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                    (notNullActivity as MainActivity).trackTabComponentExposure(requireActivity(), newIndexTabbar?.trackData)
                }
            }
        }

        mViewModel.tabFeedStreamLiveData.observe(this){pair ->
            val isRefresh = pair.first
            val result = pair.second
            if (result.isSuccess) {
                Log.d("lijiang", "feedStream  isFromCache  ${result.isFromCache}")
                if (isHomePage && isRefresh){
                    HomePageInfoSpUtil.setHomePageCommonFeedStreamResponse(result.data)
                }
                result.data.feedStreamList?.let { list->

                    if (isRefresh){
                        mConcatFeedStreamAdapter.mAdRefreshCount = isFeedStreamRefreshCount
                        skSetResponseLocalTime(list, result)
                        mConcatFeedStreamAdapter.mDataList = list
                        mConcatFeedStreamAdapter.mComponentTrackData = result.data.trackData
                        mConcatFeedStreamAdapter.componentBean = FeedComponentBean().apply {
                            sptype = homeTabBean?.sptype
                            jgspid = homeTabBean?.jgspid
                            sid = homeTabBean?.sid
                            pageType = result.data.pageType
                            pageId = result.data.pageId
                            pageName = result.data.pageName
                            pageNum = result.data.pageNum
                            pageSize = result.data.pageSize
                            componentName = result.data.componentName
                            componentPosition = result.data.componentPosition
                            componentTitle = result.data.componentTitle
                        }
                    }else{
                        skSetResponseLocalTime(list, result)
                        mConcatFeedStreamAdapter.addDataList(list)
                        mConcatFeedStreamAdapter.componentBean = FeedComponentBean().apply {
                            jgspid = homeTabBean?.jgspid
                            sptype = homeTabBean?.sptype
                            sid = homeTabBean?.sid
                            pageType = result.data.pageType
                            pageId = result.data.pageId
                            pageName = result.data.pageName
                            pageNum = result.data.pageNum
                            pageSize = result.data.pageSize
                            componentName = result.data.componentName
                            componentPosition = result.data.componentPosition
                            componentTitle = result.data.componentTitle
                        }
                    }

                    // 请求并更新折后价
                    val mList = list.filter { it.feedType == "3" }.mapNotNull { it.product }
                    getHomeFeedAfterDiscountPrice<RowsBean>(mList.toMutableList(), mConcatFeedStreamAdapter)
                    // reportPageListBuild() // 极光埋点移除：移除首页page_list_build事件
                }
            }
        }

        mViewModel.homeAptitudeStatusLiveData.observe(this){
            val notShowAptitude = mViewModel.homeAptitudeStatusLiveData.value?.msg.isNullOrEmpty() //资质是否显示中
            isShowBottomAd(notShowAptitude)
        }
    }

    fun setOnSearchDataListener(indexSearchListener: IIndexSearchListener) {
        mIndexSearchListener = indexSearchListener
    }

    /**
     * 列表页每次请求，获取追踪链路（sid）及结果集后，上报一次，分页、修改筛选都算
     * 极光埋点移除：注释掉首页page_list_build事件埋点方法
     */
    /*
    private fun reportPageListBuild() {
        ReportManager.getInstance().report(PageListBuild().apply {
            url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL //页面完整路径
            title = JGTrackManager.TrackHomePage.TITLE //页面标题
            referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
            jgspid = homeTabBean?.jgspid
            page_type = homeTabBean?.pageType
            page_id = homeTabBean?.pageId
            page_name = homeTabBean?.pageName
            page_no = mViewModel.mFeedStreamPagePosition //当前页码
            page_size = mViewModel.mFeedStreamPageSize //每页显示条数
            component_position = homeTabBean?.componentPosition //组件序号
            component_name = homeTabBean?.componentName //组件名称
            component_title = homeTabBean?.componentTitle //组件标题
            sub_module_tab = "" //子模块tab
            sub_module_left_navigation = "" //子模块左侧导航
            sub_module_top_navigation = "" //子模块顶部导航
        })
    }
    */

    // 移除极光埋点 - action_list_product_click (保留搜索页面)
    /*
    private fun reportActionListProductClick(fastEntry: FastEntry?, fastEntryItem: FastEntryItem?) {
        ReportManager.getInstance().report(ActionListProductClick().apply {
            url = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
            title = JGTrackManager.TrackHomePage.TITLE
            referrer = JGTrackManager.TrackHomePage.TRACK_HOME_INNER_URL
            jgspid = homeTabBean?.jgspid
            page_id = fastEntry?.pageId
            page_name = fastEntry?.pageName
            page_type = fastEntry?.pageType
            component_position = fastEntry?.componentPosition //组件序号
            component_name = fastEntry?.componentName //组件名称
            component_title = fastEntry?.componentTitle //组件标题
        })
    }
    */

    /**
     * 设置首页tab
     */
    private fun setTabs(tabBar: Tabbar?) {
        val tabList: MutableList<TabItem>? = tabBar?.tabContent?.tabList
        val homeConfigBean = HomeConfigBean()
        tabList?.forEachIndexed { index, tabItem ->
            homeConfigBean.bottom_text_color_def = tabItem.color
            homeConfigBean.bottom_text_color = tabItem.hoverColor
            homeConfigBean.bottom_background_image = tabItem.bgColor
            var hasOrder = false
            if (tabList.size > 4) {
                hasOrder = true
            }
            when (index) {
                0 -> {
                    homeConfigBean.bottom_first_button_img_url = tabItem.image
                    homeConfigBean.bottom_first_button_img_select_url = tabItem.hoverImage
                    homeConfigBean.bottom_first_button_text = tabItem.text
                    homeConfigBean.bottom_first_button_track_data = tabItem.trackData
                }

                //以前是5个  现在是4个 第二个隐藏掉了 所以错一位  以前的代码也改不太动了 只能这样处理了
//                1 -> {
//                    homeConfigBean.bottom_second_button_img_url = tabItem.image
//                    homeConfigBean.bottom_second_button_img_select_url = tabItem.hoverImage
//                    homeConfigBean.bottom_second_button_text = tabItem.text
//                }
                1 -> {
                    homeConfigBean.bottom_second_button_img_url = tabItem.image
                    homeConfigBean.bottom_second_button_img_select_url = tabItem.hoverImage
                    homeConfigBean.bottom_second_button_text = tabItem.text
                    homeConfigBean.bottom_second_button_link = tabItem.link
                    homeConfigBean.bottom_second_button_track_data = tabItem.trackData
                }

                2 -> {
                    homeConfigBean.bottom_third_button_img_url = tabItem.image
                    homeConfigBean.bottom_third_button_img_select_url = tabItem.hoverImage
                    homeConfigBean.bottom_third_button_text = tabItem.text
                    homeConfigBean.bottom_third_button_track_data = tabItem.trackData
                }

                3 -> {
                    if (hasOrder) {
                        homeConfigBean.bottom_fourth_button_img_url = tabItem.image
                        homeConfigBean.bottom_fourth_button_img_select_url = tabItem.hoverImage
                        homeConfigBean.bottom_fourth_button_text = tabItem.text
                        homeConfigBean.bottom_fourth_button_track_data = tabItem.trackData
                    } else {
                        homeConfigBean.bottom_fifth_button_img_url = tabItem.image
                        homeConfigBean.bottom_fifth_button_img_select_url = tabItem.hoverImage
                        homeConfigBean.bottom_fifth_button_text = tabItem.text
                        homeConfigBean.bottom_fifth_button_track_data = tabItem.trackData
                    }
                }
                4 -> {
                    homeConfigBean.bottom_fifth_button_img_url = tabItem.image
                    homeConfigBean.bottom_fifth_button_img_select_url = tabItem.hoverImage
                    homeConfigBean.bottom_fifth_button_text = tabItem.text
                    homeConfigBean.bottom_fifth_button_track_data = tabItem.trackData
                }
            }
        }
        (notNullActivity as MainActivity).setActivity(homeConfigBean)
    }

    /**
     * 给秒杀数据设置接口返回的本地时间
     * @param list MutableList<HomeFeedStreamBean>
     * @param result BaseBean<HomeFeedStreamResponse>
     */
    private fun skSetResponseLocalTime(
            list: MutableList<HomeFeedStreamBean>,
            result: BaseBean<HomeFeedStreamResponse>
    ) {
        list.forEach { feedBean -> //将获取到接口的时间 给秒杀里面赋值 有秒杀的情况会用到
            if (feedBean.getFeedType() == FEED_STREAM_PRODUCT_TYPE) { //商品类型
                feedBean.product?.actSk?.let { skBean ->
                    skBean.responseLocalTime = result.data.responseLocalTime
                }
            }
        }
    }

    private fun isShowBottomAd(isShow:Boolean){
        if (isCloseBottomAd){
            cl_ad_bottom.isVisible = false
            return
        }
        val notShowAptitude = mViewModel.homeAptitudeStatusLiveData.value?.msg.isNullOrEmpty() //资质是否显示中
        if (notShowAptitude){
            cl_ad_bottom.isVisible = isShow
        }else{
            cl_ad_bottom.isVisible = false
        }

    }

    private fun handleScroll(){
        (requireActivity() as? MainActivity)?.handleToTopBtn(scrollDistance > 500)
    }

    private fun initBottomAd(bottomAdvertisementContent: BottomAdvertisementContent?) {
        bottomAdvertisementContent?.bottomAdvertisementList?.let { list->
            if (list.size>0){
                if (isFirstLoadBottomAd){
                    isFirstLoadBottomAd = false
                    bottomAdPosition = 0
                }else{
                    if (bottomAdvertisementContent.isNeedRandom()){ //
                        bottomAdPosition = Random.nextInt(0,list.size-1)
                    }else{
                        bottomAdPosition = (bottomAdPosition+1) % list.size
                    }
                }

                if (bottomAdPosition > list.size-1) bottomAdPosition = 0 //以防万一
                list[bottomAdPosition].url?.let {mUrl->
                    if (mUrl.endsWith("gif")){
                        Glide.with(requireActivity())
                                .load(mUrl)
                                .asGif()
                                .listener(object : RequestListener<String?, GifDrawable?> {
                                    override fun onException(
                                            e: Exception?,
                                            model: String?,
                                            target: Target<GifDrawable?>?,
                                            isFirstResource: Boolean
                                    ): Boolean {
                                        isShowBottomAd(false)
                                        return false
                                    }

                                    override fun onResourceReady(
                                            resource: GifDrawable?,
                                            model: String?,
                                            target: Target<GifDrawable?>?,
                                            isFromMemoryCache: Boolean,
                                            isFirstResource: Boolean
                                    ): Boolean {
                                        isShowBottomAd(true)
                                        val map = HashMap<String,Any>().apply {
                                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                                        }
                                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_BOTTOM_ADVERTISING_EXPOSURE,map)
                                        return false
                                    }
                                })
                                .override(720,80) //设置这个是为了回调监听 不然不知道多大会不回调
                                .placeholder(R.drawable.jiazaitu_min)
                                .error(R.drawable.jiazaitu_min)
                                .into(iv_ad)
                    }else{
                        Glide.with(requireActivity())
                                .load(mUrl)
                                .asBitmap()
                                .listener(object : RequestListener<String?, Bitmap?> {
                                    override fun onException(
                                            e: Exception?,
                                            model: String?,
                                            target: Target<Bitmap?>?,
                                            isFirstResource: Boolean
                                    ): Boolean {
                                        isShowBottomAd(false)
                                        return false
                                    }

                                    override fun onResourceReady(
                                            resource: Bitmap?,
                                            model: String?,
                                            target: Target<Bitmap?>?,
                                            isFromMemoryCache: Boolean,
                                            isFirstResource: Boolean
                                    ): Boolean {
                                        isShowBottomAd(true)
                                        val map = HashMap<String,Any>().apply {
                                            put(TrackManager.FIELD_PAGE_ID,getPageId())
                                        }
                                        TrackManager.exposureEventTrack(TrackManager.TrackHome.EVENT_BOTTOM_ADVERTISING_EXPOSURE,map)
                                        return false
                                    }
                                })
                                .dontAnimate()
                                .override(720,80) //设置这个是为了回调监听 不然不知道多大会不回调
                                .placeholder(R.drawable.jiazaitu_min)
                                .error(R.drawable.jiazaitu_min)
                                .into(iv_ad)
                    }

                }?: kotlin.run {
                    isShowBottomAd(false)
                }

                mCurrentBottomAdUrl = list[bottomAdPosition].hrefUrl?:""
            }else{
                isShowBottomAd(false)
            }
        }?:run {
            isShowBottomAd(false)
        }
    }

    private fun initRecycleView() {
        mConcatAdapter = ConcatAdapter().apply {
            addAdapter(mConcatAtmosphereHeadPictureAdapter.apply {
                navigation = tabName
            })
            addAdapter(mCapsuleAdvertisementAdapter.apply {
                navigation = tabName
            })
            addAdapter(mConcatFastEntryAdapter.apply {
	            navigation = tabName
            })
            addAdapter(mConcatPorcelainTilesAdapter.apply {
                navigation = tabName
            })
            addAdapter(mConcatFrequentPurchaseListAdapter.apply {
                navigation = tabName
            })
            addAdapter(mConcatSuperValueListAdapter.apply {
                navigation = tabName
            })
            addAdapter(mConcatFeedStreamAdapter.apply {
                navigation = tabName
            })
        }

        rv_content.layoutManager = StaggeredGridLayoutManager(HOME_SPAN_COUNT, StaggeredGridLayoutManager.VERTICAL)
        if (rv_content.itemDecorationCount == 0) {
            rv_content.addItemDecoration(HomeFeedStreamAdapter.HomeFeedStreamItemDecoration())
        }
        rv_content.adapter = mConcatAdapter
    }

    override fun onResume() {
        super.onResume()
        if (parentFragment?.isHidden == true) return

        if (isFirst) {
            if (isHomePage) {
//                initCacheData()
            }
            isFirst = false
            isFeedStreamRefreshCount = 0
            toRequest(true)
        }
        handleScroll()
        LiveEventBus.get(LiveEventBusManager.HomeBus.BUS_SHOW_BIG_WHEEL,Boolean::class.java).post(true)
    }

    override fun toTop() {
        super.toTop()
        rv_content?.smoothScrollToPosition(0)
    }

    //显示新手引导页
    private fun showNewbieGuidePage() { //        Animation enterAnimation = new AlphaAnimation(0f, 1f);
        //        enterAnimation.setDuration(200);
        //        enterAnimation.setFillAfter(true);
        //
        //        Animation exitAnimation = new AlphaAnimation(1f, 0f);
        //        exitAnimation.setDuration(200);
        //        exitAnimation.setFillAfter(true);
        if (!isHomePage) return //首页显示
        if (isFinishedNewBieGuide) return
        //重置新手引导
        val sharedPreferences = requireActivity().getSharedPreferences(NewbieGuide.TAG, Activity.MODE_PRIVATE)
        sharedPreferences.edit().putInt(NEWBIE_GUIDE_LABEL, 0).apply()
        NewbieGuide.with(this)
                .setLabel(NEWBIE_GUIDE_LABEL) //.setShowCounts(3) 控制次数 默认只会显示一次
                .setOnPageChangedListener { page: Int ->
                    LogUtil.d(HomeSteadyLayoutFragmentV3.TAG, "当前新手引导层显示Page：$page")

                    val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,page+1) }
                    TrackManager.exposureEventTrack(EVENT_BEGINNER_GUIDANCE_EXPOSURE,map)
                }
                .addGuidePage(GuidePage.newInstance()
                        .setLayoutRes(R.layout.guide_page_first) //点击消失的id,有下一页会自动下一页
                        .setEverywhereCancelable(false)
                        //                        .setEnterAnimation(enterAnimation)//进入动画
                        //                        .setExitAnimation(exitAnimation)//退出动画
                        .setOnLayoutInflatedListener { view: View, controller: Controller ->
                            val tvSkip = view.findViewById<TextView>(R.id.tv_skip)
                            val tvGuidePageFirst = view.findViewById<TextView>(R.id.tv_guide_page_first_next)
                            val clRoot = view.findViewById<ConstraintLayout>(R.id.cl_root)

                            tvSkip.setOnClickListener { v: View? ->
                                newBieGuideFinished(controller)
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,1) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_SKIP_CLICK,map)
                            }

                            tvGuidePageFirst.setOnClickListener {
                                controller.showPage(1) //下一页
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,1) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_NEXT_STEP_CLICK,map)
                            }

                            clRoot.setOnClickListener {
                                controller.showPage(1) //下一页
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,1) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_BLANK_CLICK,map)
                            }

                        })
                .addGuidePage(GuidePage.newInstance()
                        .setLayoutRes(R.layout.guide_page_seconde)
                        .setEverywhereCancelable(false)
                        //                        .setEnterAnimation(enterAnimation)//进入动画
                        //                        .setExitAnimation(exitAnimation)//退出动画
                        .setOnLayoutInflatedListener { view: View, controller: Controller ->
                            val tvSkip = view.findViewById<TextView>(R.id.tv_skip)
                            val tvGuidePageSecond = view.findViewById<TextView>(R.id.tv_guide_page_second_next)
                            val clRoot = view.findViewById<ConstraintLayout>(R.id.cl_root)

                            tvSkip.setOnClickListener { v: View? ->
                                newBieGuideFinished(controller)
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,2) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_SKIP_CLICK,map)
                            }

                            tvGuidePageSecond.setOnClickListener {
                                controller.showPage(2) //下一页
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,2) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_NEXT_STEP_CLICK,map)
                            }

                            clRoot.setOnClickListener {
                                controller.showPage(2) //下一页
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,2) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_BLANK_CLICK,map)
                            }

                        })
//                .addGuidePage(GuidePage.newInstance()
//                        .setLayoutRes(R.layout.guide_page_third)
//                        .setEverywhereCancelable(false)
//                        //                        .setEnterAnimation(enterAnimation)//进入动画
//                        //                        .setExitAnimation(exitAnimation)//退出动画
//                        .setOnLayoutInflatedListener { view: View, controller: Controller ->
//                            val tvSkip = view.findViewById<TextView>(R.id.tv_skip)
//                            val tvGuidePageThird = view.findViewById<TextView>(R.id.tv_guide_page_third_next)
//                            val clRoot = view.findViewById<ConstraintLayout>(R.id.cl_root)
//
//                            tvSkip.setOnClickListener { v: View? ->
//                                newBieGuideFinished(controller)
//                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,3) }
//                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_SKIP_CLICK,map)
//                            }
//
//                            tvGuidePageThird.setOnClickListener {
//                                controller.showPage(3) //下一页
//                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,3) }
//                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_NEXT_STEP_CLICK,map)
//                            }
//
//                            clRoot.setOnClickListener {
//                                controller.showPage(3) //下一页
//                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,3) }
//                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_BLANK_CLICK,map)
//                            }
//                        })
                .addGuidePage(GuidePage.newInstance()
                        .setLayoutRes(R.layout.guide_page_fourth)
                        .setEverywhereCancelable(false)
                //                        .setEnterAnimation(enterAnimation)//进入动画
                        //                        .setExitAnimation(exitAnimation)//退出动画
                        .setOnLayoutInflatedListener { view: View, controller: Controller ->
                            val tvGuidePageThird = view.findViewById<TextView>(R.id.tv_guide_page_fourth_finish)
                            val clRoot = view.findViewById<ConstraintLayout>(R.id.cl_root)

                            tvGuidePageThird.setOnClickListener {
                                newBieGuideFinished(controller)
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,3) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_START_PURCHASING_CLICK,map)
                            }

                            clRoot.setOnClickListener {
                                newBieGuideFinished(controller)
                                val map = HashMap<String,Int>().apply { put(TrackManager.FIELD_OFFSET,3) }
                                TrackManager.clickEventTrack(TrackManager.TrackNewBieGuide.EVENT_BLANK_CLICK,map)
                            }
                        }
                ).show()
    }

    fun newBieGuideFinished(controller:Controller){
        controller.remove()
        isFinishedNewBieGuide = true
        SharePreUtil.putBoolean(requireActivity(), KEY_IS_FINISHED_NEWBIE_GUIDE,true)
    }

    private fun getPageId() = if (isHomePage) TrackManager.TrackHome.TRACK_HOME_PAGE_ID else TRACK_HOME_TAB_PAGE_ID


    private fun addTrackField2Url(url:String,pageId:Int,module:Int,offset: String,sourceType:String="",anchorProductId:String="",anchorProductStatus:String=""):String{
        val mUrl = url.trim()
        return if (mUrl.endsWith("html")){
            "$mUrl?&page_id=$pageId&module=$module&offset=$offset&sourceType=$sourceType&anchorProductId=$anchorProductId&anchorProductStatus=$anchorProductStatus"
        }else{
            "$mUrl&page_id=$pageId&module=$module&offset=$offset&sourceType=$sourceType&anchorProductId=$anchorProductId&anchorProductStatus=$anchorProductStatus"
        }
    }

    private fun porcelainTilesItemClickTrack(
            where: Int,
            type: Int,
            offset: Int,
            content: String
    ) {
        var map = HashMap<String, Any>()
        val trackEvent = when (where) {
            WHERE_CLICK_MAIN_TITLE -> {

                map.apply {
                    put(TrackManager.FIELD_TYPE, type)
                    put(TrackManager.FIELD_OFFSET, offset)
                    put(TrackManager.FIELD_CONTENT, content)
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_1)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }

                TrackManager.TrackHome.EVENT_ACTION_TITLE_CLICK
            }

            else -> {
                map.apply {
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_1)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.TrackHome.EVENT_ACTION_BLANK_CLICK
            }
        }
        TrackManager.clickEventTrack(trackEvent, map)
    }

    private fun capsuleClickTrack(
            model: Int,
            offset: Int,
            capsuleAdvertisementItemBean: CapsuleAdvertisementItemBean
    ) {
        val map = HashMap<String, Any>().apply {
            put(TrackManager.FIELD_MODE, model)
            put(TrackManager.FIELD_OFFSET, offset)
            put(TrackManager.FIELD_URL, capsuleAdvertisementItemBean.hrefUrl ?: "")
            put(TrackManager.FIELD_PAGE_ID, getPageId())
        }
        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_CAROUSEL_BANNER_CLICK, map)
    }

    private fun fastEntryClickTrack(
            model: Int,
            offset: String,
            url: String
    ) {
        val map = HashMap<String, Any>().apply {
            put(TrackManager.FIELD_MODE, model)
            put(TrackManager.FIELD_OFFSET, offset)
            put(TrackManager.FIELD_URL, url)
            put(TrackManager.FIELD_PAGE_ID, getPageId())
        }
        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_ICON_CLICK, map)
    }

    private fun porcelainTilesProductClickTrack(
            type: Int,
            offset: Int,
            rowBean: RowsBean
    ) {
        var map = HashMap<String, Any>().apply {
            put(TrackManager.FIELD_TYPE, type)
            put(TrackManager.FIELD_OFFSET, offset)
            put(TrackManager.FIELD_SKU_ID, rowBean.id)
            put(TrackManager.FIELD_SKU_NAME, rowBean.productName)
            put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_1)
            put(TrackManager.FIELD_PAGE_ID, getPageId())
        }

        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_PRODUCT_CLICK, map)
    }

    private fun frequentPurchaseClickTrack(
            where: Int,
            type: Int,
            content: String
    ) {
        var map = HashMap<String, Any>()
        val trackEvent = when (where) {
            WHERE_CLICK_MAIN_TITLE -> {

                map.apply {
                    put(TrackManager.FIELD_TYPE, type)
                    put(TrackManager.FIELD_CONTENT, content)
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_2)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }

                TrackManager.TrackHome.EVENT_ACTION_TITLE_CLICK
            }

            else -> {
                map.apply {
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_2)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.TrackHome.EVENT_ACTION_BLANK_CLICK
            }
        }
        TrackManager.clickEventTrack(trackEvent, map)
    }

    private fun frequentPurchaseProductClickTrack(
            offset: Int,
            rowBean: RowsBean
    ) {
        var map = HashMap<String, Any>().apply {
            put(TrackManager.FIELD_OFFSET, offset)
            put(TrackManager.FIELD_SKU_ID, rowBean.id)
            put(TrackManager.FIELD_SKU_NAME, rowBean.productName)
            put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_2)
            put(TrackManager.FIELD_PAGE_ID, getPageId())
        }

        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_PRODUCT_CLICK, map)
    }

    private fun superValueClickTrack(
            where: Int,
            type: Int,
            content: String
    ) {
        var map = HashMap<String, Any>()
        val trackEvent = when (where) {
            WHERE_CLICK_MAIN_TITLE -> {

                map.apply {
                    put(TrackManager.FIELD_TYPE, type)
                    put(TrackManager.FIELD_CONTENT, content)
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_2)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }

                TrackManager.TrackHome.EVENT_ACTION_TITLE_CLICK
            }

            else -> {
                map.apply {
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_3)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }
                TrackManager.TrackHome.EVENT_ACTION_BLANK_CLICK
            }
        }
        TrackManager.clickEventTrack(trackEvent, map)
    }

    private fun superValueProductClickTrack(
            offset: Int,
            rowBean: RowsBean
    ) {
        var map = HashMap<String, Any>().apply {
            put(TrackManager.FIELD_OFFSET, offset)
            put(TrackManager.FIELD_SKU_ID, rowBean.id)
            put(TrackManager.FIELD_SKU_NAME, rowBean.productName)
            put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_3)
            put(TrackManager.FIELD_PAGE_ID, getPageId())
        }

        TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_PRODUCT_CLICK, map)
    }

    private fun feedStreamClickTrack(
            clickType: Int,
            position: Int,
            url: String,
            rowBean: RowsBean?
    ) {
        when (clickType) {

            AD_BANNER_VIEW_TYPE -> {
                var map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_OFFSET, position + 1) //下标从1开始
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_4)
                    put(TrackManager.FIELD_URL, url)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }

                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_CAROUSEL_FEED_CLICK, map)
            }

            AD_VIEW_TYPE -> {
                var map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_OFFSET, position)
                    put(TrackManager.FIELD_SKU_ID, "")
                    put(TrackManager.FIELD_SKU_NAME, "")
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_4)
                    put(TrackManager.FIELD_TYPE, 2) //1-商品；2-图片
                    put(TrackManager.FIELD_URL, url)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }

                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_PRODUCT_CLICK, map)
            }

            COMMODITY_VIEW_TYPE -> {
                var map = HashMap<String, Any>().apply {
                    put(TrackManager.FIELD_OFFSET, position)
                    put(TrackManager.FIELD_SKU_ID, rowBean?.id?:"")
                    put(TrackManager.FIELD_SKU_NAME, rowBean?.productName?:"")
                    put(TrackManager.FIELD_MODULE, TrackManager.TrackHome.TRACK_HOME_MODULE_4)
                    put(TrackManager.FIELD_TYPE, 1) //1-商品；2-图片
                    put(TrackManager.FIELD_URL, url)
                    put(TrackManager.FIELD_PAGE_ID, getPageId())
                }

                TrackManager.clickEventTrack(TrackManager.TrackHome.EVENT_ACTION_PRODUCT_CLICK, map)
            }
        }
    }

    interface IIndexSearchListener {
        fun onIndexSearch(indexSearch: NewSearchBox?)
    }

    override fun onHandleFragmentPv() {
        trackHomePv(requireActivity(), mTrackData)
    }

    /**
     * 首页PV
     */
    fun trackHomePv(context: Context, trackData: TrackData?) {
        resetReportRecord()
        trackHomePagePv(context, trackData)
//        if (notNullActivity != null) {
//            (notNullActivity as MainActivity).trackTabComponentExposure(requireActivity(), null)
//        }
        if (indexSearch != null) {
            mIndexSearchListener?.onIndexSearch(indexSearch)
        }
        reComponentExposure()
    }

    /**
     * 下拉刷新清理埋点缓存
     */
    private fun resetReportRecord() {
        getAdapterList().forEach {
            it.resetExposureRecord()
        }
    }

    private fun reComponentExposure() {
//        getAdapterList().forEach {
//            it.reComponentExposure()
//        }
        try {
            val reDataList = mConcatFeedStreamAdapter.mDataList
            reDataList[0].reLoadTag ++
            mConcatFeedStreamAdapter.notifyDataSetChanged()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getAdapterList() = listOf(
        mConcatAtmosphereHeadPictureAdapter,
        mCapsuleAdvertisementAdapter,
        mConcatFastEntryAdapter,
        mConcatFrequentPurchaseListAdapter,
        mConcatPorcelainTilesAdapter,
        mConcatSuperValueListAdapter,
        mConcatFeedStreamAdapter
    )
}