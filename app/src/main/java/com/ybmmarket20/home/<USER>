package com.ybmmarket20.home

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.util.SparseArray
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.StringUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsPriceDiscount
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.bean.getSingleStepSpannableForShopList
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.AdapterUtils.notifyAndControlLoadmoreStatus
import com.ybmmarket20.utils.analysis.*
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.view.homesteady.getPriceStringBuilder
import com.ybmmarketkotlin.bean.ShopItemBean
import com.ybmmarketkotlin.bean.ShopListBean
import com.ybmmarketkotlin.utils.TextWithPrefixTag
import kotlinx.android.synthetic.main.fragment_home_steady_layout.*
import kotlinx.android.synthetic.main.fragment_home_steady_layout_v2.*
import kotlinx.android.synthetic.main.fragment_shop_list_tab.*
import org.json.JSONObject

class ShopTabFragment : BaseFragment() {
    override fun initTitle() {}

    override fun getUrl(): String? = null

    var shopList: MutableList<ShopItemBean?> = mutableListOf()
    private lateinit var shopItemAdapter: ShopItemAdapter
    private lateinit var loadMoreParams: RequestParams
    private lateinit var recyclerPool: RecyclerView.RecycledViewPool
    private var br: BroadcastReceiver? = null
    private var isLoaded = false

    private var sort = 0        // 排序方式 0:默认，1:最新
    private var shopPropertyCode = "self" // self 自营 、 other POP

    override fun initData(content: String?) {


    }

    private fun initData(){
        shopPropertyCode = arguments?.getString("shopPropertyCode").toString()

        rg_tab?.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_defalt -> {
                    sort = 0
                    getData(true)
                    shopListDefaultClick(
                            AnalysisConst.ShopList.SHOP_LIST_DEFAULT_CLICK,
                            "$sort",
                            "默认"
                                        )
                }
                R.id.rb_new -> {
                    sort = 1
                    shopListNewestClick(
                            AnalysisConst.ShopList.SHOP_LIST_NEWEST_CLICK,
                            "$sort",
                            "最新入驻"
                                       )
                    getData(true)
                }
            }
        }

        shopItemAdapter = ShopItemAdapter(R.layout.item_shop_list, shopList)

        val emptyView = layoutInflater.inflate(R.layout.layout_empty_view, null)
        emptyView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
                                                       )
        shopItemAdapter.emptyView = emptyView
        shopItemAdapter.setOnLoadMoreListener({ getData(false) }, rv_shop_list)

        recyclerPool = RecyclerView.RecycledViewPool()
        rv_shop_list.adapter = shopItemAdapter
        rv_shop_list.layoutManager = LinearLayoutManager(notNullActivity)

        smartrefresh.setOnRefreshListener { getData(true) }
        smartrefresh.autoRefresh()

        initBroadCastReceiver()
    }

    override fun onVisibleChanged(isVisible: Boolean) {
        super.onVisibleChanged(isVisible)
        if (isVisible && !isLoaded){
            isLoaded = true
            initData()
        }
    }

    /**
     * 初始化广播
     */
    private fun initBroadCastReceiver() {
        br = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (IntentCanst.ACTION_SWITCH_USER == intent.action) {
                    getData(true)
                }
            }
        }
        val intentFilter = IntentFilter(IntentCanst.ACTION_SWITCH_USER)
        LocalBroadcastManager.getInstance(notNullActivity)
            .registerReceiver(br as BroadcastReceiver, intentFilter)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (br != null) LocalBroadcastManager.getInstance(notNullActivity).unregisterReceiver(br!!)
    }

    private fun getData(isFirst: Boolean) {

        if (isFirst) {
            rv_shop_list.smoothScrollToPosition(0)
            showProgress()
        }

        try {
            if (!isFirst && !TextUtils.equals(loadMoreParams.paramsMap["merchantId"], SpUtil.getMerchantid())) {
                return
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        val params: RequestParams = getRequestParams(isFirst).apply { url = AppNetConfig.SHOP_LIST }
        addAnalysisRequestParams(params, mFlowData)
        HttpManager.getInstance().post(params, object : BaseResponse<ShopListBean>() {
            override fun onSuccess(
                content: String?,
                obj: BaseBean<ShopListBean>?,
                shopListBean: ShopListBean?
            ) {
                if (isFirst) {
                    smartrefresh.post {
                        smartrefresh.finishRefresh()
                    }
                    shopItemAdapter.resetTraceShop()
                }
                if (obj != null && obj.isSuccess() && shopListBean != null) {
                    updateShopListData(isFirst, shopListBean)
                }
                dismissProgress()
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                if (isFirst) {
                    smartrefresh.post {
                        smartrefresh.finishRefresh(false)
                    }
                }
                dismissProgress()
            }
        })


    }

    private fun updateShopListData(isFirst: Boolean, shopListBean: ShopListBean) {
        loadMoreParams = shopListBean.requestParams

        //brandBean.sptype, brandBean.spid, brandBean.sid
        updateFlowData(mFlowData, shopListBean.sptype, shopListBean.spid, shopListBean.sid)
        shopItemAdapter.flowData = mFlowData
        // 更新资质状态
        if (shopListBean.licenseStatus != 0) {
            AuditStatusSyncUtil.getInstance().updateLicenseStatus(shopListBean.licenseStatus)
        }
        notifyAndControlLoadmoreStatus(
            shopListBean.dataList,
            shopItemAdapter,
            isFirst,
            shopListBean.isEnd
        )

        // 请求并更新折后价
        getAndUpdateDiscoutPrice(shopListBean.shopListInfo, shopItemAdapter)

    }

    private fun getAndUpdateDiscoutPrice(
        shopInfoList: MutableList<ShopItemBean>,
        shopItemAdapter: ShopItemAdapter
    ) {

        // 如果更新的列表为null则返回
        if (shopInfoList == null || shopItemAdapter?.data == null) return

        // 组建获取折后价的入参
        val rowsIds = StringBuffer()
        shopInfoList?.forEach {
            it?.productInfo?.filter {row->
                !row.rangePriceBean.isStep()
            }?.forEach {
                rowsIds.append(it.id).append(",")
            }
        }
        if (TextUtils.isEmpty(rowsIds)) return
        rowsIds.deleteCharAt(rowsIds.length - 1)

        // 获取折后价
        val requestParams = RequestParams()
        requestParams.put("skuIds", rowsIds.toString())
        requestParams.url = AppNetConfig.LIST_PRODUCT_DISCOUNT
        HttpManager.getInstance()
            .post(requestParams, object : BaseResponse<List<RowsPriceDiscount?>?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<List<RowsPriceDiscount?>?>?,
                    rowsPriceDiscounts: List<RowsPriceDiscount?>?
                ) {
                    if (obj != null && !rowsPriceDiscounts.isNullOrEmpty()) {

                        rowsPriceDiscounts.forEach {
                            it?.let {
                                var skuid = it.skuId
                                var price = it.price
                                for (shoplistItem in shopItemAdapter.data as MutableList<ShopItemBean>) {
                                    for (rowItem in shoplistItem.productInfo ?: mutableListOf()) {
                                        if (skuid == rowItem.id) {
                                            rowItem.showPriceAfterDiscount = price
                                        }
                                    }
                                }
                            }
                        }
                        shopItemAdapter.notifyDataSetChanged()

                    }
                }
            })


    }

    private fun getRequestParams(isFirst: Boolean): RequestParams {
        var requestParams: RequestParams
        if (isFirst) {
            requestParams = RequestParams().apply {
                put("shopPropertyCode", shopPropertyCode)
                put("sort", "${sort}")
                put("merchantId", SpUtil.getMerchantid())
                put("sptype", mFlowData.spType)
                put("spid", mFlowData.spId)
                put("spid", mFlowData.sId)
                XyyIoUtil.checkSpTypeField(mFlowData, false)
            }
        } else {
            requestParams = loadMoreParams
        }
        return requestParams
    }

    override fun getParams() = null

    override fun getLayoutId() = R.layout.fragment_shop_list_tab
    private fun jgTrackResourceClick(position: Int,module:String,shopId:String,shopName:String){
        val properties: HashMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShopList.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShopList.TITLE
        properties[JGTrackManager.FIELD.FIELD_URL] = ShopListFragment.JG_TRACK_URL
        properties[JGTrackManager.FIELD.FIELD_URL_DOMAIN] = ShopListFragment.JG_TRACK_URL
        properties[JGTrackManager.FIELD.FIELD_REFERRER] = ShopListFragment.JG_TRACK_URL
        properties[JGTrackManager.FIELD.FIELD_MODULE] = module
        properties[JGTrackManager.FIELD.FIELD_RESOURCE_NAME] = "店铺卡片"
        properties[JGTrackManager.FIELD.FIELD_RESOURCE_TYPE] = "卡片"
        properties[JGTrackManager.FIELD.FIELD_RANK] = position+1
        properties[JGTrackManager.FIELD.FIELD_SHOP_ID] = shopId
        properties[JGTrackManager.FIELD.FIELD_SHOP_NAME] = shopName
        // JGTrackManager.eventTrack(requireActivity(), JGTrackManager.TrackShopList.EVENT_RESOURCE_CLICK,properties) // 极光埋点移除：移除店铺列表resource_click事件
    }

    private fun getModuleStr():String{
        var module = if (shopPropertyCode == "self") "自营" else "合作商家"
        module += if (sort == 1) "-最新入驻" else "-默认"
        return module
    }

    inner class ShopItemAdapter : YBMBaseAdapter<ShopItemBean?> {

        var flowData: BaseFlowData? = null
        private val traceShopData = SparseArray<String>()
        // key: 店铺Id     value:当时埋点的时间戳
        private val productViewTrackMap = hashMapOf<String, Long>()
        private val trackDuration = 2 * 60 * 1000 //2分钟内不上报


        constructor(layoutResId: Int, data: MutableList<ShopItemBean?>) : super(layoutResId, data)

        @SuppressLint("ClickableViewAccessibility")//拦截了recyclerview的点击事件
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShopItemBean?) {

            t?.let {
                // 店铺信息
//                baseViewHolder?.setText(R.id.tv_shopname, it.showName)
                val qualityTagList =
                    if (it.qualityTag == null) listOf<TagBean>() else listOf(it.qualityTag)
                val tvTitle = baseViewHolder?.getView<TextView>(R.id.tv_shopname)
                tvTitle?.TextWithPrefixTag(qualityTagList, it.showName)
                baseViewHolder?.getView<ImageView>(R.id.iv_shop_logo)
                    ?.let { iv ->
                        ImageUtil.loadRoundCornerImage(
                            notNullActivity,
                            AppNetConfig.CDN_HOST + t.appLogo,
                            iv,
                            4
                        )
                    }
                baseViewHolder?.getView<TextView>(R.id.tv_onsale)?.text =
                    getActivityStr(it.shelvesDesc, it.shelves.toString())
                baseViewHolder?.getView<TextView>(R.id.tv_sold)?.text = getActivityStr(
                    it.salesVolumeDesc, it.salesVolume
                        ?: ""
                )
                baseViewHolder?.getView<TextView>(R.id.tv_pingkage)?.text = it.freightTips


                val tvCoupon = baseViewHolder?.getView<TextView>(R.id.tv_coupon)
                val tvActive = baseViewHolder?.getView<TextView>(R.id.tv_active)

                tvCoupon?.visibility = View.GONE
                tvActive?.visibility = View.GONE

                it.activityInfo?.forEach {
                    when (it.activityType) {
                        1 -> baseViewHolder?.getView<TextView>(R.id.tv_coupon)?.apply {
                            visibility = View.VISIBLE
                            text = it.activityContent
                            val drawable =
                                ContextCompat.getDrawable(mContext, R.drawable.icon_shop_coupon)
                                    ?.apply { setBounds(0, 0, minimumWidth, minimumHeight) }
                            StringUtils.modifyTextViewDrawable(this, drawable, 0)
                            compoundDrawablePadding = UiUtils.dp2px(4)
                        }

                        4 -> baseViewHolder?.getView<TextView>(R.id.tv_active)?.apply {
                            visibility = View.VISIBLE
                            text = it.activityContent
                            val drawable =
                                ContextCompat.getDrawable(mContext, R.drawable.icon_shop_cu)
                                    ?.apply { setBounds(0, 0, minimumWidth, minimumHeight) }
                            StringUtils.modifyTextViewDrawable(this, drawable, 0)
                            compoundDrawablePadding = UiUtils.dp2px(4)
                        }
                    }

                }
                baseViewHolder?.setOnClickListener(R.id.cl_shop_info) {
                    var mUrl = t.newAppLink?:""
                    if (!t.newAppLink .isNullOrEmpty()){
                        mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                                Pair<String,Any>(IntentCanst.JG_ENTRANCE,JGTrackManager.TrackShopList.TITLE),
                                Pair<String,Any>(IntentCanst.JG_REFERRER,<EMAIL>()),
                                Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,JGTrackManager.TrackShopList.TITLE),
                                Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,"${JGTrackManager.TrackShopList.TITLE}")))
                    }
                    RoutersUtils.open(mUrl)
                    shopListShopClick(
                        AnalysisConst.ShopList.SHOP_LIST_SHOP_CLICK,
                        t.shopPatternCode,
                        t.orgId,
                        flowData
                    )

                    jgTrackResourceClick(baseViewHolder?.bindingAdapterPosition?:0,getModuleStr(),t.shopCode?:"",t.showName?:"")
                }

                val rvShopProduct = baseViewHolder?.getView<RecyclerView>(R.id.rv_shop_product)!!
                rvShopProduct.setOnTouchListener { _, motionEvent ->
                    if (motionEvent.action == MotionEvent.ACTION_UP) {
                        baseViewHolder?.getView<View>(R.id.cl_shop_info)?.performClick();
                    }
                    false
                }

                val shopProductItemAdapter = ShopProductItemAdapter(
                    R.layout.item_shop_product_list,
                    t.productInfo?.take(3)?.toMutableList(),
                    t.newAppLink
                )
                rvShopProduct.setRecycledViewPool(recyclerPool)
                rvShopProduct.adapter = shopProductItemAdapter
                rvShopProduct.layoutManager = GridLayoutManager(mContext, 3)
                baseViewHolder.let { holder ->
                    if (traceShopData.get(holder.bindingAdapterPosition) == null) {
                        XyyIoUtil.track(
                            AnalysisConst.ShopList.SHOP_LIST_SHOP_EXPOSURE,
                            JSONObject().apply {
                                put("storetype", it.shopPatternCode)
                                put("id", it.orgId)
                                put("sptype", flowData?.spType ?: "")
                                put("spid", flowData?.spId ?: "")
                                put("sid", flowData?.sId ?: "")
                                XyyIoUtil.checkSpTypeField(flowData, true)
                            })
                        traceShopData.put(holder.bindingAdapterPosition, t.orgId)
                    }
                }
                val tagView = baseViewHolder.getView<ShopNameWithTagView>(R.id.snwv_shop_item_tag)
                tagView.visibility = if (it.shopPropTags?.isNotEmpty() == true) {
                    tagView.bindData(it.shopPropTags)
                    View.VISIBLE
                } else View.GONE

                val shopId = t.shopCode?:""
                if (shopId.isEmpty()) return
                productViewTrackMap[shopId]?.let {time->
                    //极光曝光埋点
                    if (System.currentTimeMillis() - time > trackDuration){
                        productViewTrackMap[shopId] = System.currentTimeMillis()
                    }
                }?: kotlin.run {
                    productViewTrackMap[shopId] = System.currentTimeMillis()
                }


            }
        }

        private fun getActivityStr(shelvesDesc: String?, toString: String): CharSequence? {
            val activityStr = SpannableStringBuilder()
            activityStr.append(shelvesDesc?.replace("xxx", toString, true))
            val startIndex = shelvesDesc?.indexOf("xxx") ?: 0
            val endIndex = startIndex + toString.length
            activityStr.setSpan(
                ForegroundColorSpan(UiUtils.getColor(R.color.text_00B377)),
                startIndex,
                endIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            return activityStr
        }

        /**
         * 清空埋点缓存数据
         */
        fun resetTraceShop() {
            traceShopData.clear()
        }

    }

    inner class ShopProductItemAdapter(layoutResId: Int, data: MutableList<RowsBean>?, private val router: String?) :
        YBMBaseAdapter<RowsBean>(layoutResId, data) {

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: RowsBean?) {
            val ivIcon: ImageView? = baseViewHolder.getView<ImageView>(R.id.iv_product)
            val tvPriceAfterDiscount =
                baseViewHolder.getView<TextView>(R.id.tv_price_after_discount)
            val tvPrice = baseViewHolder.getView<TextView>(R.id.tv_price)
            val spellGroup = baseViewHolder.getView<Group>(R.id.groupSpellPrice)
            val tvSpellPrice = baseViewHolder.getView<TextView>(R.id.tvSpellPrice)
            val tvPriceTitle = baseViewHolder.getView<TextView>(R.id.tv_price_title)

            ivIcon?.let {
                ImageUtil.load(
                    baseViewHolder.getConvertView().context,
                    AppNetConfig.LORD_IMAGE + t?.imageUrl,
                    ivIcon
                )
            }
            val priceSpannable = if (t?.actPt != null) {
                tvPriceTitle.text = "拼团价"
                getGoodsSpellGroupStepPrice(t.actPt?.isStepPrice()?: false, t.actPt?.minSkuPrice, t.actPt?.assemblePrice?.toString()?: "")
            } else if (t?.actPgby != null) {
                tvPriceTitle.text = "包邮价"
                getPriceStringBuilder("${t.actPgby.assemblePrice?: ""}")
            } else null
            tvSpellPrice.text = priceSpannable
            // baseViewHolder.setOnClickListener(R.id.ll_product_item, { RoutersUtils.open("ybmpage://productdetail/${t?.productId}") })
            baseViewHolder.itemView.setOnClickListener {
                if (!router.isNullOrEmpty()) {
                    if (router.contains("?")) {
                        "$router&anchorCsuId=${t?.productId}"
                    } else {
                        "$router?anchorCsuId=${t?.productId}"
                    }.let(RoutersUtils::open)
                }
            }
            /*
             *
             *   0 : 正常展示价格
             *   1 ： 展示 "暂无购买权限"
             *   2 :  展示  "价格签署协议可见"
             *   3 :  展示  "价格认证资质可见"
             *  */
            when (t?.showPriceType()) {

                0 -> {
                    tvPrice?.typeface = Typeface.DEFAULT_BOLD
                    tvPrice?.textSize = 10f
                    tvPrice?.text = if (t.rangePriceBean.isStep()) {
                        t.rangePriceBean.getSingleStepSpannableForShopList()
                    } else {
                        t.showPriceStrNoPrefix
                    }
                    tvPrice?.visibility = View.VISIBLE
                    tvPriceAfterDiscount?.visibility = View.VISIBLE
                    // 正常展示价格中的 折后价
                    if(t.actPt != null){
                        spellGroup?.visibility = View.VISIBLE
                        tvPrice.visibility = View.GONE
                        tvPriceAfterDiscount.visibility = View.GONE
                    } else if (t.actPgby != null) {
                        spellGroup?.visibility = View.VISIBLE
                        tvPrice.visibility = View.GONE
                        tvPriceAfterDiscount.visibility = View.GONE
                    } else if (!TextUtils.isEmpty(t.showPriceAfterDiscount)) {
                        tvPriceAfterDiscount?.text = t.showPriceAfterDiscount
                        tvPriceAfterDiscount?.visibility = View.VISIBLE
                        spellGroup?.visibility = View.GONE
                    } else  {
                        tvPriceAfterDiscount?.visibility = View.GONE
                        spellGroup?.visibility = View.GONE
                    }
                    try {
                        if (!t.showPriceAfterDiscount.isNullOrEmpty()) {
                            val priceAfterDiscount = t.showPriceAfterDiscount.replace("折后约￥", "").toDouble()
                            if (priceAfterDiscount > t.fob) {
                                tvPriceAfterDiscount.visibility = View.GONE
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    context?.let {
                        tvPrice?.setTextColor(
                            ContextCompat.getColor(
                                it,
                                R.color.color_ff2121
                            )
                        )
                    }
                }
                -1 -> {
                    tvPrice?.typeface = Typeface.DEFAULT
                    tvPrice?.textSize = 13f
                    tvPrice?.text = t.controlTitle
                    tvPriceAfterDiscount?.visibility = View.GONE
                    tvPrice?.setTextColor(Color.parseColor("#FF834A"))
                    spellGroup?.visibility = View.GONE
                }
            }

        }

    }

}