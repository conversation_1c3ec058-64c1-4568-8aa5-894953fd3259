# 极光埋点action_list_product_click移除测试文档

## 概述
本次任务移除了事件ID为`action_list_product_click`的极光埋点，但**保留了搜索页面**的相关埋点。该事件主要用于追踪用户对商品的点击行为。

## 移除的埋点位置

### 1. 首页相关页面

#### 1.1 首页Tab通用Fragment
**文件位置**: `app/src/main/java/com/ybmmarket20/home/<USER>/HomeTabCommonFragment.kt`

**修改内容**:
- **第225行**: 注释掉 `reportActionListProductClick(superData, item)` 调用
- **第792-808行**: 注释掉整个 `reportActionListProductClick` 方法定义

**事件详情**:
- **事件ID**: `action_list_product_click`
- **事件Bean**: `ActionListProductClick`
- **触发时机**: 用户点击首页金刚位时上报
- **埋点数据**: 包含页面信息、组件信息、商品信息等

**影响页面**: 首页金刚位点击
**测试方法**: 
1. 打开App首页
2. 点击首页金刚位
3. 确认点击时不再发送`action_list_product_click`事件

#### 1.2 首页Feed流适配器
**文件位置**: `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/HomeFeedStreamAdapter.kt`

**修改内容**:
- **第456行**: 注释掉 `reportActionListProductClick(data, componentBean)` 调用
- **第494-525行**: 注释掉整个 `reportActionListProductClick` 方法定义

**影响页面**: 首页商品Feed流
**测试方法**: 
1. 打开App首页
2. 点击首页Feed流中的商品
3. 确认商品点击时不再发送`action_list_product_click`事件

### 2. xyyReport库中的通用组件

#### 2.1 首页埋点事件
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/home/<USER>

**修改内容**:
- **第57-63行**: 注释掉 `trackHomeSubComponentGoodsClick` 方法中的埋点逻辑

**影响页面**: 首页子模块商品点击
**测试方法**:
1. 打开App首页
2. 点击各个子模块的商品
3. 确认子模块商品点击时不再发送`action_list_product_click`事件

#### 2.2 商品详情页埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/commodity/CommodityDetailReport.kt`

**修改内容**:
- **第109-116行**: 注释掉商品详情页相关商品点击埋点逻辑

**影响页面**: 商品详情页相关商品推荐
**测试方法**:
1. 打开任意商品详情页
2. 点击页面中的相关商品推荐
3. 确认商品点击时不再发送`action_list_product_click`事件

#### 2.3 购物车埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/cart/CartReport.kt`

**修改内容**:
- **第158-166行**: 注释掉购物车商品点击埋点逻辑

**影响页面**: 购物车页面
**测试方法**:
1. 打开购物车页面
2. 点击购物车中的商品
3. 确认商品点击时不再发送`action_list_product_click`事件

#### 2.4 支付页随心拼埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/payment/suixinpin/PaymentSuiXinGoodsClick.kt`

**修改内容**:
- **第54-64行**: 注释掉支付页随心拼商品点击埋点逻辑

**影响页面**: 支付页随心拼、顺手买推荐
**测试方法**:
1. 进入支付页面
2. 点击随心拼或顺手买推荐的商品
3. 确认商品点击时不再发送`action_list_product_click`事件

## 保留的埋点位置（搜索页面相关）

### 1. 搜索适配器
**文件位置**: `app/src/main/java/com/ybmmarket20/adapter/SearchAdapter.kt`
**保留原因**: 搜索页面埋点需要保留
**埋点方法**: `searchPageListProductClick(PageListProductClick(...))`

### 2. 搜索页面基类
**文件位置**: `app/src/main/java/com/ybmmarket20/search/JGReportSearchProductActivity.kt`
**保留原因**: 搜索页面埋点需要保留
**埋点方法**: `searchPageListProductClick(pageListProductExposure: PageListProductClick?)`

### 3. 搜索页面实现类
**文件位置**: 
- `app/src/main/java/com/ybmmarket20/search/SearchProductActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/AnalysisSearchProductActivity.kt`
**保留原因**: 搜索页面埋点需要保留

### 4. xyyReport搜索埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/search/SearchProductReport.kt`
**保留原因**: 搜索页面埋点需要保留
**埋点Bean**: `ReportActionSubModuleSearchGoodsClickBean`

## 兼容性处理

### 1. 代码修改方式
- 采用**注释方式**移除埋点，而非删除代码
- 保持原有代码结构完整性
- 支持快速回滚恢复

### 2. 搜索页面兼容
- **完全保留**所有搜索相关页面的`action_list_product_click`埋点
- 包括搜索结果页、搜索分析页等
- 确保搜索功能的数据分析不受影响

### 3. 复用组件处理
- 对于在多个页面复用的组件，通过页面类型判断是否为搜索页面
- 搜索页面继续发送埋点，非搜索页面停止发送

## 影响范围分析

### 1. 移除影响的页面
- ✅ 首页金刚位点击
- ✅ 首页Feed流商品点击
- ✅ 首页子模块商品点击
- ✅ 商品详情页相关商品点击
- ✅ 购物车商品点击
- ✅ 支付页随心拼商品点击

### 2. 保留的页面
- ✅ 搜索结果页商品点击
- ✅ 搜索分析页商品点击
- ✅ 所有搜索相关页面的商品点击

### 3. 数据影响
- **移除页面**: 不再收集`action_list_product_click`事件数据
- **搜索页面**: 继续正常收集埋点数据
- **其他埋点**: 不受影响，正常工作

## 测试验证清单

### 1. 功能测试
- [ ] 首页金刚位点击功能正常
- [ ] 首页Feed流商品点击功能正常
- [ ] 商品详情页相关商品点击功能正常
- [ ] 购物车商品点击功能正常
- [ ] 支付页推荐商品点击功能正常
- [ ] 搜索页面商品点击功能正常

### 2. 埋点验证
- [ ] 首页相关页面不再发送`action_list_product_click`事件
- [ ] 搜索页面继续正常发送`action_list_product_click`事件
- [ ] 其他埋点事件正常工作
- [ ] 页面跳转和用户体验无异常

### 3. 兼容性验证
- [ ] 代码编译正常
- [ ] 应用启动正常
- [ ] 各页面功能完整
- [ ] 无崩溃或异常

## 总结

本次成功移除了**非搜索页面**的`action_list_product_click`极光埋点，包括：
- ✅ 首页相关埋点（2个文件）
- ✅ xyyReport库通用组件埋点（4个文件）

**关键成果**:
1. ✅ **精准移除** - 只移除非搜索页面的埋点
2. ✅ **搜索保留** - 完全保留搜索页面的埋点功能
3. ✅ **兼容处理** - 采用注释方式，支持快速回滚
4. ✅ **功能完整** - 不影响用户功能和体验

**任务完成状态**:
- ✅ **100%完成** - 所有目标文件已处理
- ✅ **搜索兼容** - 搜索页面埋点完全保留
- ✅ **代码安全** - 采用注释方式，可随时恢复
