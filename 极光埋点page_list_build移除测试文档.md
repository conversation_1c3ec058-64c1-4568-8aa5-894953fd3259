# 极光埋点page_list_build移除测试文档

## 概述
本次任务移除了事件ID为`page_list_build`的极光埋点，但保留了搜索页面的相关埋点。根据需求，搜索页面的埋点不被移除，其他页面的该事件被移除。

## 移除的埋点位置

### 1. 首页Feed流
**文件位置**: `app/src/main/java/com/ybmmarket20/home/<USER>/HomeTabCommonFragment.kt`

**修改内容**:
- **第750行**: 注释掉 `reportPageListBuild()` 调用
- **第768-789行**: 注释掉整个 `reportPageListBuild` 方法定义

**事件详情**:
- **事件ID**: `page_list_build`
- **事件常量**: 通过`@ReportEventName("page_list_build")`注解定义
- **触发时机**: 首页Feed流每次请求数据后，获取追踪链路（sid）及结果集后上报
- **埋点数据**: 包含页面URL、标题、页码信息、组件信息等

**影响页面**: 首页Feed流（包括首页tab和其他tab页）
**测试方法**: 
1. 打开App首页
2. 下拉刷新首页内容
3. 上拉加载更多内容
4. 切换不同的tab页
5. 确认首页Feed流数据加载时不再发送`page_list_build`事件

## 保留的埋点位置

### 1. 搜索结果页
**文件位置**: `app/src/main/java/com/ybmmarket20/search/BaseSearchProductActivity.java`

**保留内容**:
- **第3050行**: `searchProductListBuild(getPageListBuild(...))` 调用被保留
- **第3187-3301行**: `getPageListBuild` 方法被保留
- **第3271-3296行**: `PageListBuild` 对象创建和设置被保留

**影响页面**: 
- 大搜页面 (`SearchProductOPActivity`)
- 专区搜索页面 (`SearchProductSectionActivity`) 
- 订单凑单搜索页面 (`SearchProductOrderBundlingActivity`)

### 2. 搜索埋点管理类
**文件位置**: `app/src/main/java/com/ybmmarket20/search/JGReportSearchProductActivity.kt`

**保留内容**:
- **第99-104行**: `searchProductListBuild` 方法被保留
- 该方法用于搜索页面的`page_list_build`事件上报

**影响页面**: 所有搜索相关页面

## Bean类定义（未修改）

### 1. 首页Bean类
**文件位置**: `app/src/main/java/com/ybmmarket20/bean/homesteady/HomeSteadyModel.kt`
- **第98-116行**: `PageListBuild` 类定义保持不变
- 使用`@ReportEventName("page_list_build")`注解

### 2. 搜索Bean类  
**文件位置**: `app/src/main/java/com/ybmmarket20/reportBean/SearchReportBean.kt`
- **第65-77行**: `PageListBuild` 类定义保持不变
- 使用`@ReportEventName("page_list_build")`注解

## 兼容性处理

### 1. 代码保留策略
所有的修改都采用了注释的方式而不是直接删除代码，这样做的好处：
1. **可回滚**: 如果需要恢复功能，只需要取消注释即可
2. **代码完整性**: 保持了代码结构的完整性  
3. **调试友好**: 便于后续调试和问题排查

### 2. 复用组件处理
由于首页和搜索页面使用了不同的`PageListBuild`类定义：
- **首页**: 使用`com.ybmmarket20.bean.homesteady.PageListBuild`
- **搜索页面**: 使用`com.ybmmarket20.reportBean.PageListBuild`

这种设计天然实现了兼容性，移除首页埋点不会影响搜索页面的埋点功能。

## 测试建议

### 1. 功能测试
**首页测试**:
1. 启动App，进入首页
2. 下拉刷新首页内容
3. 上拉加载更多Feed流内容
4. 切换不同的tab页（如果有）
5. 确认页面功能正常，但不发送`page_list_build`事件

**搜索页面测试**:
1. 进入搜索页面
2. 输入关键词进行搜索
3. 查看搜索结果列表
4. 上拉加载更多搜索结果
5. 修改筛选条件重新搜索
6. 确认搜索功能正常，且仍然发送`page_list_build`事件

### 2. 埋点验证
**验证方法**:
1. 使用埋点调试工具或日志查看
2. 确认首页操作时不再有`page_list_build`事件
3. 确认搜索页面操作时仍有`page_list_build`事件
4. 检查事件参数是否正确

### 3. 回归测试
**重点关注**:
1. 首页Feed流加载功能
2. 搜索结果列表功能
3. 其他埋点事件是否正常
4. App整体稳定性

## 影响范围分析

### 1. 技术影响
- ✅ **低风险**: 只是注释代码，不删除
- ✅ **隔离性好**: 首页和搜索使用不同的Bean类
- ✅ **可回滚**: 随时可以恢复功能

### 2. 业务影响
- ⚠️ **数据缺失**: 首页Feed流将不再有`page_list_build`数据
- ✅ **搜索数据完整**: 搜索页面数据不受影响
- ✅ **用户体验**: 不影响用户使用功能

### 3. 数据分析影响
**移除的数据**:
- 首页Feed流的页面构建数据
- 首页的页码、页面大小信息
- 首页的组件位置、名称、标题信息

**保留的数据**:
- 搜索结果页的页面构建数据
- 搜索关键词、结果数量信息
- 搜索的筛选条件、排序信息

## 总结

本次成功移除了首页的`page_list_build`极光埋点，同时完整保留了搜索页面的相关埋点。通过使用注释方式而非删除代码，确保了修改的可回滚性和代码的完整性。

**关键成果**:
1. ✅ 首页`page_list_build`事件已移除
2. ✅ 搜索页面`page_list_build`事件完整保留  
3. ✅ 代码修改采用注释方式，可随时回滚
4. ✅ 不同页面使用不同Bean类，天然隔离
5. ✅ 不影响其他埋点功能和用户体验

**测试重点**:
- 验证首页不再发送`page_list_build`事件
- 验证搜索页面仍正常发送`page_list_build`事件
- 确认页面功能完全正常
