# 极光埋点action_product_button_click移除测试文档

## 概述
本次任务移除了事件ID为`action_product_button_click`的极光埋点，但**保留了搜索页面**的相关埋点。该事件主要用于追踪用户对商品按钮的点击行为，包括加购、减少、数量修改等操作。

## 移除的埋点位置

### 1. 商品详情页
**文件位置**: `app/src/main/java/com/ybmmarket20/fragments/CommodityFragment.java`

**修改内容**:
- **第1259-1267行**: 注释掉商品详情页按钮点击埋点
- **第1574-1581行**: 注释掉拼团页面按钮点击埋点

**事件详情**:
- **事件ID**: `action_product_button_click`
- **事件Bean**: `ReportPDButtonClick`
- **触发时机**: 用户在商品详情页点击加购、立即参团等按钮时上报
- **埋点数据**: 包含商品信息、按钮名称、按钮描述、操作方向等

**影响页面**: 商品详情页、拼团页面
**测试方法**: 
1. 打开商品详情页
2. 点击加购、立即购买、立即参团等按钮
3. 确认按钮点击时不再发送`action_product_button_click`事件

### 2. 购物车页面
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/cart/CartReport.kt`

**修改内容**:
- **第190-198行**: 注释掉购物车商品按钮点击埋点

**影响页面**: 购物车页面
**测试方法**: 
1. 打开购物车页面
2. 点击商品的加号、减号、数量输入、收藏、找相似等按钮
3. 确认按钮点击时不再发送`action_product_button_click`事件

### 3. 支付页随心拼
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/payment/suixinpin/PaymentSuiXinGoodsClick.kt`

**修改内容**:
- **第82-92行**: 注释掉支付页随心拼商品按钮点击埋点

**影响页面**: 支付页随心拼、顺手买模块
**测试方法**: 
1. 进入支付页面
2. 在随心拼或顺手买模块点击商品的加购、加号、数量等按钮
3. 确认按钮点击时不再发送`action_product_button_click`事件

### 4. 加购弹窗（非搜索商品）
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/common/addCart/AddCartPopupWindowReport.kt`

**修改内容**:
- **第62-78行**: 注释掉非搜索商品的按钮点击埋点，保留搜索商品埋点

**影响页面**: 各种加购弹窗（除搜索页面外）
**测试方法**: 
1. 在非搜索页面触发加购弹窗
2. 点击弹窗中的按钮
3. 确认非搜索商品不再发送`action_product_button_click`事件
4. 确认搜索商品继续正常发送埋点

### 5. 拼团弹窗
**文件位置**: `app/src/main/java/com/ybmmarket20/view/ShowSpellGroupPopWindow.java`

**修改内容**:
- **第293-321行**: 注释掉拼团弹窗按钮点击埋点

**影响页面**: 拼团弹窗
**测试方法**: 
1. 打开拼团弹窗
2. 点击确定按钮
3. 确认按钮点击时不再发送`action_product_button_click`事件

### 6. 商品编辑布局
**文件位置**: `app/src/main/java/com/ybmmarket20/view/ProductEditLayoutNew.java`

**修改内容**:
- **第390-434行**: 注释掉商品列表页底部弹窗按钮点击埋点

**影响页面**: 商品列表页底部弹窗
**测试方法**: 
1. 在商品列表页触发底部弹窗
2. 点击弹窗中的按钮
3. 确认按钮点击时不再发送`action_product_button_click`事件

## 保留的埋点位置（搜索页面）

### 1. 搜索适配器
**文件位置**: `app/src/main/java/com/ybmmarket20/adapter/SearchAdapter.kt`
**保留原因**: 搜索页面埋点需要保留
**埋点方法**: `ReportPDButtonClick` 在搜索页面中的使用

### 2. 搜索页面基类
**文件位置**: `app/src/main/java/com/ybmmarket20/search/JGReportSearchProductActivity.kt`
**保留原因**: 搜索页面埋点需要保留
**埋点方法**: 搜索页面相关的埋点基础方法

### 3. 搜索页面实现类
**文件位置**: 
- `app/src/main/java/com/ybmmarket20/search/AnalysisSearchProductActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/SearchProductActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/SearchProductOPActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/SearchProductSectionActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/SearchProductOrderBundlingActivity.kt`
**保留原因**: 搜索页面埋点需要保留

### 4. xyyReport搜索埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/search/SearchProductReport.kt`
**保留原因**: 搜索页面埋点需要保留
**埋点Bean**: `ReportActionSearchProductButtonClickBean`

### 5. 加购弹窗搜索部分
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/common/addCart/AddCartPopupWindowReport.kt`
**保留原因**: 搜索页面埋点需要保留
**保留内容**: 当`isSearch == true`时的`ReportActionSearchProductButtonClickBean`埋点

## 兼容性处理

### 1. 代码修改方式
- 采用**注释方式**移除埋点，而非删除代码
- 保持原有代码结构完整性
- 支持快速回滚恢复

### 2. 搜索页面兼容
- **完全保留**所有搜索相关页面的`action_product_button_click`埋点
- 包括搜索结果页、搜索分析页、专区搜索页、订单凑单搜索页等
- 确保搜索功能的数据分析不受影响

### 3. 复用组件处理
- 对于在多个页面复用的组件（如加购弹窗），通过页面类型判断是否为搜索页面
- 搜索页面继续发送埋点，非搜索页面停止发送
- 保持组件功能完整性，只移除埋点逻辑

## 影响范围分析

### 1. 移除影响的页面
- ✅ 商品详情页按钮点击
- ✅ 购物车商品按钮点击
- ✅ 支付页随心拼商品按钮点击
- ✅ 非搜索页面加购弹窗按钮点击
- ✅ 拼团弹窗按钮点击
- ✅ 商品列表页底部弹窗按钮点击

### 2. 保留的页面
- ✅ 搜索结果页商品按钮点击
- ✅ 搜索分析页商品按钮点击
- ✅ 专区搜索页商品按钮点击
- ✅ 订单凑单搜索页商品按钮点击
- ✅ 搜索页面加购弹窗按钮点击
- ✅ 所有搜索相关页面的商品按钮点击

### 3. 数据影响
- **移除页面**: 不再收集`action_product_button_click`事件数据
- **搜索页面**: 继续正常收集埋点数据
- **其他埋点**: 不受影响，正常工作

## 测试验证清单

### 1. 功能测试
- [ ] 商品详情页按钮点击功能正常
- [ ] 购物车商品操作功能正常
- [ ] 支付页随心拼功能正常
- [ ] 各种弹窗功能正常
- [ ] 搜索页面商品操作功能正常

### 2. 埋点验证
- [ ] 非搜索页面不再发送`action_product_button_click`事件
- [ ] 搜索页面继续正常发送`action_product_button_click`事件
- [ ] 其他埋点事件正常工作
- [ ] 页面跳转和用户体验无异常

### 3. 兼容性验证
- [ ] 代码编译正常
- [ ] 应用启动正常
- [ ] 各页面功能完整
- [ ] 无崩溃或异常

## 总结

本次成功移除了**非搜索页面**的`action_product_button_click`极光埋点，包括：
- ✅ 商品详情页埋点（1个文件）
- ✅ 购物车页面埋点（1个文件）
- ✅ 支付页随心拼埋点（1个文件）
- ✅ 加购弹窗非搜索部分埋点（1个文件）
- ✅ 拼团弹窗埋点（1个文件）
- ✅ 商品编辑布局埋点（1个文件）

**关键成果**:
1. ✅ **精准移除** - 只移除非搜索页面的埋点
2. ✅ **搜索保留** - 完全保留搜索页面的埋点功能
3. ✅ **兼容处理** - 采用注释方式，支持快速回滚
4. ✅ **功能完整** - 不影响用户功能和体验

**任务完成状态**:
- ✅ **100%完成** - 所有目标文件已处理
- ✅ **搜索兼容** - 搜索页面埋点完全保留
- ✅ **代码安全** - 采用注释方式，可随时恢复
