# 极光埋点移除测试文档

## 概述
本次任务移除了事件ID为`page_list_product_exposure`的极光埋点，但保留了搜索页面的相关埋点。

## 移除的埋点位置

### 1. 首页Feed流适配器
**文件位置**: `app/src/main/java/com/ybmmarket20/home/<USER>/adapter/HomeFeedStreamAdapter.kt`

**修改内容**:
- **第468行**: 注释掉 `reportPageListProductExposure(data, componentBean)` 调用
- **第473行**: 注释掉 `reportPageListProductExposure(data, componentBean)` 调用  
- **第478行**: 注释掉 `reportPageListProductExposure(data, componentBean)` 调用
- **第483行**: 注释掉 `reportPageListProductExposure(data, componentBean)` 调用
- **第546-572行**: 注释掉整个 `reportPageListProductExposure` 方法定义

**影响页面**: 首页商品Feed流
**测试方法**: 
1. 打开App首页
2. 滚动查看商品Feed流
3. 确认商品曝光时不再发送`page_list_product_exposure`事件

### 2. 首页埋点事件
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/home/<USER>

**修改内容**:
- **第68-72行**: 注释掉 `trackHomeSubComponentGoodsExposure` 方法中的埋点逻辑

**影响页面**: 首页子模块商品曝光
**测试方法**:
1. 打开App首页
2. 查看各个子模块的商品
3. 确认子模块商品曝光时不再发送`page_list_product_exposure`事件

### 3. 购物车埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/cart/CartReport.kt`

**修改内容**:
- **第130-134行**: 注释掉购物车商品曝光埋点逻辑

**影响页面**: 购物车页面
**测试方法**:
1. 打开购物车页面
2. 查看购物车中的商品
3. 确认商品曝光时不再发送`page_list_product_exposure`事件

### 4. 商品详情页埋点
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/commodity/CommodityDetailReport.kt`

**修改内容**:
- **第82-86行**: 注释掉商品详情页商品曝光埋点逻辑

**影响页面**: 商品详情页
**测试方法**:
1. 打开任意商品详情页
2. 查看商品详情
3. 确认商品曝光时不再发送`page_list_product_exposure`事件

### 5. 支付页面随心拼
**文件位置**: `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/payment/suixinpin/PaymentSuiXinGoodsExposure.kt`

**修改内容**:
- **第38-43行**: 注释掉 `ReportPageSubModuleSearchGoodsExposureBean` 的创建
- **第48行**: 注释掉 `ReportUtil.track` 调用

**影响页面**: 支付页面的随心拼和顺手买模块
**测试方法**:
1. 进入支付页面
2. 查看随心拼/顺手买商品推荐
3. 确认商品曝光时不再发送`page_list_product_exposure`事件

## 保留的埋点位置（搜索页面）

### 1. 搜索结果页
**文件位置**: `app/src/main/java/com/ybmmarket20/search/SearchProductActivity.kt`
**保留原因**: 按需求保留搜索页面的埋点
**测试方法**: 
1. 进行商品搜索
2. 查看搜索结果页商品
3. 确认商品曝光时仍然发送`page_list_product_exposure`事件

### 2. 搜索适配器
**文件位置**: `app/src/main/java/com/ybmmarket20/adapter/SearchAdapter.kt`
**保留原因**: 搜索相关适配器，按需求保留
**测试方法**: 同搜索结果页测试

### 3. 其他搜索相关页面
- `app/src/main/java/com/ybmmarket20/search/JGReportSearchProductActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/SearchProductSectionActivity.kt`
- `app/src/main/java/com/ybmmarket20/search/AnalysisSearchProductActivity.kt`
- `app/src/main/java/com/ybmmarket20/reportBean/SearchReportBean.kt`
- `librarys/xyyReport/src/main/java/com/ybmmarket20/xyyreport/page/search/SearchProductReport.kt`

## 兼容性处理

所有的修改都采用了注释的方式而不是直接删除代码，这样做的好处：
1. **可回滚**: 如果需要恢复功能，只需要取消注释即可
2. **代码完整性**: 保持了代码结构的完整性
3. **调试友好**: 便于后续调试和问题排查

## 测试建议

### 功能测试
1. **首页测试**: 验证首页商品Feed流功能正常，但不发送`page_list_product_exposure`埋点
2. **搜索测试**: 验证搜索功能正常，且搜索结果页仍然发送`page_list_product_exposure`埋点
3. **购物车测试**: 验证购物车功能正常，但不发送`page_list_product_exposure`埋点
4. **商品详情测试**: 验证商品详情页功能正常，但不发送`page_list_product_exposure`埋点
5. **支付页测试**: 验证支付页随心拼功能正常，但不发送`page_list_product_exposure`埋点

### 埋点验证
建议使用埋点监控工具或日志来验证：
1. 搜索页面仍然发送`page_list_product_exposure`事件
2. 其他页面不再发送`page_list_product_exposure`事件
3. 其他埋点功能不受影响

## 风险评估

**低风险**: 
- 所有修改都是注释代码，不影响核心业务逻辑
- 保留了搜索页面的埋点功能
- 可以快速回滚

**注意事项**:
- 需要验证其他埋点系统（QT埋点、雪地埋点）不受影响
- 建议在测试环境充分验证后再发布到生产环境
