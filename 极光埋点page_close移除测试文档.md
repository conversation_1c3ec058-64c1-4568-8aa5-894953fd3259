# 极光埋点page_close移除测试文档

## 概述
本次任务移除了事件ID为`product_view_close`的极光埋点，但保留了系统事件。

## 移除的埋点位置

### 1. 商品详情页关闭埋点
**文件位置**: `app/src/main/java/com/ybmmarket20/fragments/CommodityFragment.java`

**修改内容**:
- **第1328行**: 注释掉 `productViewCloseJGTrack(mDetailBean, productDetail, shopInfo)` 调用
- **第1333-1381行**: 注释掉整个 `productViewCloseJGTrack` 方法定义

**事件详情**:
- **事件ID**: `product_view_close`
- **事件常量**: `JGTrackManager.TrackProductDetail.EVENT_PRODUCT_VIEW_CLOSE`
- **触发时机**: 用户退出商品详情页时（Fragment的onDetach方法）
- **埋点数据**: 包含商品ID、商品名称、店铺信息、页面停留时长等

**影响页面**: 商品详情页
**测试方法**: 
1. 打开任意商品详情页
2. 通过各种方式退出商品详情页（返回按钮、手势返回、切换页面等）
3. 确认退出时不再发送`product_view_close`事件

## 未找到的埋点

经过全面搜索，项目中只找到了一个与"close"相关的极光埋点事件：`product_view_close`。

### 搜索范围
- 搜索关键词：`page_close`、`pageClose`、`PAGE_CLOSE`、`close`、`Close`、`CLOSE`
- 搜索范围：所有Java和Kotlin文件
- 重点关注：JGTrackManager中的事件常量定义

### 其他关闭相关事件
项目中还存在一些关闭相关的事件，但这些属于老的埋点系统（TrackManager），不是极光埋点：
- `EVENT_ACTION_FLOAT_OFF_CLICK` - 浮窗关闭（老埋点系统）
- `EVENT_ACTION_BOTTOM_ADVERTISING_OFF_CLICK` - 吸底广告关闭（老埋点系统）

这些事件使用的是`XyyIoUtil`而不是极光埋点的`AnalysysAgent`，因此不在本次移除范围内。

## 系统事件说明

根据需求，系统事件不需要移除。在Android系统中，系统事件通常指：
- Activity/Fragment的生命周期事件（由系统自动触发）
- 系统级别的页面切换事件
- 应用进入后台/前台的事件

本次移除的`product_view_close`是业务埋点，不是系统事件，因此被正确移除。

## 兼容性处理

所有的修改都采用了注释的方式而不是直接删除代码，这样做的好处：
1. **可回滚**: 如果需要恢复功能，只需要取消注释即可
2. **代码完整性**: 保持了代码结构的完整性
3. **调试友好**: 便于后续调试和问题排查

## 测试建议

### 功能测试
1. **商品详情页测试**: 
   - 从各个入口进入商品详情页（搜索结果、首页推荐、分类页面等）
   - 通过各种方式退出商品详情页（返回按钮、手势返回、点击其他页面等）
   - 确认页面功能正常，但不发送`product_view_close`埋点

### 埋点验证
建议使用埋点监控工具或日志来验证：
1. 商品详情页退出时不再发送`product_view_close`事件
2. 其他极光埋点功能不受影响（如`product_view`进入事件仍然正常）
3. 其他埋点系统（QT埋点、雪地埋点、老埋点系统）不受影响

### 回归测试
1. **商品详情页其他功能**: 确认商品详情页的其他埋点和功能不受影响
2. **页面跳转**: 确认从商品详情页跳转到其他页面功能正常
3. **数据统计**: 确认商品浏览相关的其他统计功能正常

## 风险评估

**低风险**: 
- 只移除了一个特定的关闭埋点事件
- 所有修改都是注释代码，不影响核心业务逻辑
- 可以快速回滚

**注意事项**:
- 需要验证商品详情页的其他埋点功能不受影响
- 需要确认数据分析团队了解此埋点的移除
- 建议在测试环境充分验证后再发布到生产环境

## 技术细节

### 移除的方法签名
```java
private void productViewCloseJGTrack(
    ProductDetailBeanWrapper mDetailBean,
    ProductDetailBean productDetail,
    ProductDetailBeanWrapper.ShopInfo shopInfo
)
```

### 埋点数据结构
移除的埋点包含以下数据字段：
- `FIELD_PRODUCT_ID`: 商品ID
- `FIELD_PRODUCT_NAME`: 商品名称
- `FIELD_SHOP_ID`: 店铺ID
- `FIELD_SHOP_NAME`: 店铺名称
- `FIELD_PRODUCT_TYPE`: 商品类型
- `FIELD_PRODUCT_FIRST`: 商品一级分类
- `FIELD_PRODUCT_PRESENT_PRICE`: 商品现价
- `FIELD_REFERRER`: 来源页面
- `FIELD_REFERRER_TITLE`: 来源页面标题
- `FIELD_REFERRER_MODULE`: 来源模块
- `FIELD_DURATION`: 页面停留时长

### 调用时机
- **触发位置**: `CommodityFragment.onDetach()`
- **触发条件**: Fragment从Activity分离时
- **业务含义**: 用户退出商品详情页

## 总结

本次成功移除了项目中唯一的极光埋点关闭事件`product_view_close`，该事件用于记录用户退出商品详情页的行为。移除后，用户退出商品详情页时将不再发送此埋点事件，但页面的其他功能和埋点不受影响。
